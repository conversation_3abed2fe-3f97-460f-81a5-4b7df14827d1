/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Arcade Physics World Resume Event.
 *
 * This event is dispatched by an Arcade Physics World instance when it resumes from a paused state.
 *
 * Listen to it from a Scene using: `this.physics.world.on('resume', listener)`.
 *
 * @event Phaser.Physics.Arcade.Events#RESUME
 * @type {string}
 * @since 3.0.0
 */
module.exports = 'resume';
