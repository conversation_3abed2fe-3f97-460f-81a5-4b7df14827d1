<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Shinobi Chronicles</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: rgba(52, 73, 94, 0.8);
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .result {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .error {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🥷 Test de Shinobi Chronicles</h1>
    <p>Cette page permet de tester les différents systèmes du jeu.</p>

    <div class="test-section">
        <h2>🔧 Test des Systèmes</h2>
        <button class="test-button" onclick="testGameConfig()">Test GameConfig</button>
        <button class="test-button" onclick="testGameState()">Test GameState</button>
        <button class="test-button" onclick="testChakraSystem()">Test Chakra</button>
        <button class="test-button" onclick="testCombatSystem()">Test Combat</button>
        <button class="test-button" onclick="testSaveSystem()">Test Sauvegarde</button>
        <div id="system-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Test des Données</h2>
        <button class="test-button" onclick="testEnemiesData()">Test Ennemis</button>
        <button class="test-button" onclick="testItemsData()">Test Objets</button>
        <button class="test-button" onclick="testJutsuData()">Test Jutsu</button>
        <div id="data-results"></div>
    </div>

    <div class="test-section">
        <h2>🎮 Test du Jeu</h2>
        <button class="test-button" onclick="startTestGame()">Démarrer Test Complet</button>
        <button class="test-button" onclick="resetTestData()">Reset Données</button>
        <div id="game-results"></div>
    </div>

    <div class="test-section">
        <h2>📈 Statistiques</h2>
        <div id="stats-display">
            <p>Cliquez sur "Démarrer Test Complet" pour voir les statistiques.</p>
        </div>
    </div>

    <!-- Scripts du jeu -->
    <script src="js/core/GameConfig.js"></script>
    <script src="js/core/GameState.js"></script>
    <script src="js/systems/SaveSystem.js"></script>
    <script src="js/systems/ChakraSystem.js"></script>
    <script src="js/systems/CombatSystem.js"></script>
    <script src="js/systems/FogOfWar.js"></script>

    <script>
        // Fonctions de test
        function log(message, isError = false) {
            console.log(message);
            return `<div class="${isError ? 'error' : 'result'}">${message}</div>`;
        }

        function testGameConfig() {
            const results = document.getElementById('system-results');
            let output = '<h3>Test GameConfig</h3>';
            
            try {
                // Test de la configuration
                output += log(`✅ GameConfig chargé: ${typeof GameConfig}`);
                output += log(`✅ Clans disponibles: ${Object.keys(GameConfig.clans).length}`);
                output += log(`✅ Régions disponibles: ${Object.keys(GameConfig.world.regions).length}`);
                
                // Test des fonctions utilitaires
                const xpForLevel5 = GameConfig.getXPForLevel(5);
                output += log(`✅ XP pour niveau 5: ${xpForLevel5}`);
                
                const rank = GameConfig.getRankForLevel(10);
                output += log(`✅ Rang niveau 10: ${rank.name} - ${rank.title}`);
                
            } catch (error) {
                output += log(`❌ Erreur GameConfig: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function testGameState() {
            const results = document.getElementById('system-results');
            let output = '<h3>Test GameState</h3>';
            
            try {
                // Test de l'état du jeu
                output += log(`✅ GameState initialisé: ${typeof gameState}`);
                output += log(`✅ Joueur niveau: ${gameState.player.level}`);
                output += log(`✅ HP: ${gameState.player.hp}/${gameState.player.maxHp}`);
                output += log(`✅ Chakra: ${gameState.player.chakra}/${gameState.player.maxChakra}`);
                output += log(`✅ Position: (${gameState.player.position.x}, ${gameState.player.position.y})`);
                output += log(`✅ Inventaire: ${gameState.inventory.items.length} objets, ${gameState.inventory.ryo} Ryo`);
                
                // Test des méthodes
                const oldXP = gameState.player.xp;
                gameState.gainXP(50);
                output += log(`✅ Gain XP: ${oldXP} → ${gameState.player.xp}`);
                
            } catch (error) {
                output += log(`❌ Erreur GameState: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function testChakraSystem() {
            const results = document.getElementById('system-results');
            let output = '<h3>Test ChakraSystem</h3>';
            
            try {
                output += log(`✅ ChakraSystem initialisé: ${typeof chakraSystem}`);
                
                const oldChakra = gameState.player.chakra;
                const canSpend = chakraSystem.spendChakra(5, 'test');
                output += log(`✅ Dépense chakra (5): ${canSpend ? 'Succès' : 'Échec'}`);
                output += log(`✅ Chakra: ${oldChakra} → ${gameState.player.chakra}`);
                
                chakraSystem.restoreChakra(3, 'test');
                output += log(`✅ Restauration chakra: ${gameState.player.chakra}`);
                
                const percentage = chakraSystem.getChakraPercentage();
                output += log(`✅ Pourcentage chakra: ${percentage.toFixed(1)}%`);
                
            } catch (error) {
                output += log(`❌ Erreur ChakraSystem: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function testCombatSystem() {
            const results = document.getElementById('system-results');
            let output = '<h3>Test CombatSystem</h3>';
            
            try {
                output += log(`✅ CombatSystem initialisé: ${typeof combatSystem}`);
                output += log(`✅ État combat: ${combatSystem.battleState}`);
                
                // Test des données de jutsu
                const jutsu = combatSystem.getJutsuData('dragon_slash');
                if (jutsu) {
                    output += log(`✅ Jutsu trouvé: ${jutsu.name} (${jutsu.chakraCost} chakra)`);
                } else {
                    output += log(`⚠️ Jutsu non trouvé`, true);
                }
                
            } catch (error) {
                output += log(`❌ Erreur CombatSystem: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function testSaveSystem() {
            const results = document.getElementById('system-results');
            let output = '<h3>Test SaveSystem</h3>';
            
            try {
                output += log(`✅ SaveSystem initialisé: ${typeof saveSystem}`);
                
                // Test de sauvegarde
                const saveResult = saveSystem.saveGame(1, false);
                output += log(`✅ Sauvegarde: ${saveResult ? 'Succès' : 'Échec'}`);
                
                // Test des informations de slots
                const slotsInfo = saveSystem.getSaveSlotInfo();
                output += log(`✅ Slots de sauvegarde: ${slotsInfo.length} slots`);
                
                slotsInfo.forEach((slot, index) => {
                    if (slot.exists) {
                        output += log(`  Slot ${slot.slot}: ${slot.playerName} Niv.${slot.level}`);
                    } else {
                        output += log(`  Slot ${slot.slot}: Vide`);
                    }
                });
                
            } catch (error) {
                output += log(`❌ Erreur SaveSystem: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        async function testEnemiesData() {
            const results = document.getElementById('data-results');
            let output = '<h3>Test Données Ennemis</h3>';
            
            try {
                const response = await fetch('data/enemies.json');
                const enemies = await response.json();
                
                output += log(`✅ Ennemis chargés: ${enemies.length} ennemis`);
                
                const levels = enemies.map(e => e.level);
                const minLevel = Math.min(...levels);
                const maxLevel = Math.max(...levels);
                output += log(`✅ Niveaux: ${minLevel} à ${maxLevel}`);
                
                const bosses = enemies.filter(e => e.is_boss);
                output += log(`✅ Boss: ${bosses.length} boss`);
                
                // Afficher quelques ennemis
                enemies.slice(0, 3).forEach(enemy => {
                    output += log(`  ${enemy.name} (Niv.${enemy.level}) - ${enemy.hp} HP`);
                });
                
            } catch (error) {
                output += log(`❌ Erreur chargement ennemis: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        async function testItemsData() {
            const results = document.getElementById('data-results');
            let output = '<h3>Test Données Objets</h3>';
            
            try {
                const response = await fetch('data/items.json');
                const items = await response.json();
                
                output += log(`✅ Objets chargés: ${items.length} objets`);
                
                const types = [...new Set(items.map(i => i.type))];
                output += log(`✅ Types: ${types.join(', ')}`);
                
                const rarities = [...new Set(items.map(i => i.rarity))];
                output += log(`✅ Raretés: ${rarities.join(', ')}`);
                
                // Afficher quelques objets
                items.slice(0, 3).forEach(item => {
                    output += log(`  ${item.name} (${item.rarity}) - ${item.type}`);
                });
                
            } catch (error) {
                output += log(`❌ Erreur chargement objets: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        async function testJutsuData() {
            const results = document.getElementById('data-results');
            let output = '<h3>Test Données Jutsu</h3>';
            
            try {
                const response = await fetch('data/jutsu.json');
                const jutsu = await response.json();
                
                output += log(`✅ Jutsu chargés: ${jutsu.length} techniques`);
                
                const types = [...new Set(jutsu.map(j => j.type))];
                output += log(`✅ Types: ${types.join(', ')}`);
                
                const elements = [...new Set(jutsu.map(j => j.element))];
                output += log(`✅ Éléments: ${elements.join(', ')}`);
                
                const ranks = [...new Set(jutsu.map(j => j.rank))];
                output += log(`✅ Rangs: ${ranks.join(', ')}`);
                
                // Afficher quelques jutsu
                jutsu.slice(0, 3).forEach(technique => {
                    output += log(`  ${technique.name} (${technique.rank}) - ${technique.chakra_cost} chakra`);
                });
                
            } catch (error) {
                output += log(`❌ Erreur chargement jutsu: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function startTestGame() {
            const results = document.getElementById('game-results');
            let output = '<h3>Test Complet du Jeu</h3>';
            
            try {
                // Simuler une session de jeu
                output += log(`🎮 Démarrage du test complet...`);
                
                // Test de création de personnage
                gameState.state.player.clan = 'ryujin';
                gameState.state.player.name = 'Test Ninja';
                output += log(`✅ Personnage créé: ${gameState.player.name} (${gameState.player.clan})`);
                
                // Test de mouvement
                gameState.updatePlayerPosition(30, 30);
                output += log(`✅ Mouvement: Position (${gameState.player.position.x}, ${gameState.player.position.y})`);
                
                // Test de gain d'XP
                const oldLevel = gameState.player.level;
                gameState.gainXP(200);
                if (gameState.player.level > oldLevel) {
                    output += log(`🎉 Level Up! Niveau ${gameState.player.level} atteint!`);
                }
                
                // Test d'ajout d'objets
                gameState.addItem('chakra_pill', 5);
                gameState.addItem('healing_salve', 3);
                output += log(`✅ Objets ajoutés: ${gameState.inventory.items.length} types d'objets`);
                
                // Test de dépense de chakra
                const chakraSpent = chakraSystem.spendChakra(10, 'test_jutsu');
                output += log(`✅ Utilisation jutsu: ${chakraSpent ? 'Succès' : 'Échec'}`);
                
                // Test de sauvegarde
                const saved = saveSystem.saveGame(1, false);
                output += log(`✅ Sauvegarde: ${saved ? 'Succès' : 'Échec'}`);
                
                output += log(`🎯 Test complet terminé avec succès!`);
                
                // Mettre à jour les statistiques
                updateStats();
                
            } catch (error) {
                output += log(`❌ Erreur test complet: ${error.message}`, true);
            }
            
            results.innerHTML = output;
        }

        function resetTestData() {
            const results = document.getElementById('game-results');
            
            try {
                gameState.resetGame();
                localStorage.clear();
                results.innerHTML = log(`✅ Données de test réinitialisées`);
                updateStats();
            } catch (error) {
                results.innerHTML = log(`❌ Erreur reset: ${error.message}`, true);
            }
        }

        function updateStats() {
            const statsDiv = document.getElementById('stats-display');
            const player = gameState.player;
            
            statsDiv.innerHTML = `
                <h4>📊 Statistiques du Joueur</h4>
                <p><strong>Nom:</strong> ${player.name || 'Ninja Sans Nom'}</p>
                <p><strong>Clan:</strong> ${player.clan || 'Aucun'}</p>
                <p><strong>Niveau:</strong> ${player.level}</p>
                <p><strong>HP:</strong> ${player.hp}/${player.maxHp}</p>
                <p><strong>Chakra:</strong> ${player.chakra}/${player.maxChakra}</p>
                <p><strong>XP:</strong> ${player.xp}/${player.xpToNext}</p>
                <p><strong>Position:</strong> (${player.position.x}, ${player.position.y})</p>
                <p><strong>Région:</strong> ${player.currentRegion}</p>
                <p><strong>Ryo:</strong> ${gameState.inventory.ryo}</p>
                <p><strong>Objets:</strong> ${gameState.inventory.items.length} types</p>
                <p><strong>Zones explorées:</strong> ${gameState.world.exploredTiles.length} tuiles</p>
            `;
        }

        // Initialisation
        window.addEventListener('load', () => {
            console.log('🥷 Page de test chargée');
            updateStats();
        });
    </script>
</body>
</html>
