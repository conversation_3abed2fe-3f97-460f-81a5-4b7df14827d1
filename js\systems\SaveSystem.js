/**
 * Système de sauvegarde avancé pour Shinobi Chronicles
 * Gère la sauvegarde locale, la compression et les slots multiples
 */

class SaveSystem {
    constructor() {
        this.maxSaveSlots = 3;
        this.autoSaveInterval = 30000; // 30 secondes
        this.compressionEnabled = true;
        this.autoSaveTimer = null;
        
        this.initializeAutoSave();
    }

    // Initialise la sauvegarde automatique
    initializeAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setInterval(() => {
            if (gameState.settings.autoSave) {
                this.quickSave();
            }
        }, this.autoSaveInterval);
    }

    // Sauvegarde rapide (slot actuel)
    quickSave() {
        try {
            const currentSlot = gameState.state?.meta?.saveSlot || 1;
            return this.saveGame(currentSlot, false); // false = pas de notification
        } catch (error) {
            console.error('Erreur lors de la sauvegarde rapide:', error);
            return false;
        }
    }

    // Sauvegarde dans un slot spécifique
    saveGame(slot = 1, showNotification = true) {
        if (slot < 1 || slot > this.maxSaveSlots) {
            console.error(`Slot de sauvegarde invalide: ${slot}`);
            return false;
        }

        try {
            // Préparer les données de sauvegarde
            const saveData = this.prepareSaveData(slot);
            
            // Compresser si activé
            const finalData = this.compressionEnabled ? 
                this.compressData(saveData) : JSON.stringify(saveData);
            
            // Sauvegarder dans localStorage
            localStorage.setItem(`shinobi_save_${slot}`, finalData);
            
            // Mettre à jour les métadonnées
            this.updateSaveMetadata(slot);
            
            if (showNotification) {
                this.showSaveNotification(`Jeu sauvegardé (Slot ${slot})`);
            }
            
            console.log(`Sauvegarde réussie dans le slot ${slot}`);
            return true;
            
        } catch (error) {
            console.error("Erreur lors de la sauvegarde:", error);
            this.showSaveNotification("Erreur de sauvegarde!", "error");
            return false;
        }
    }

    // Prépare les données pour la sauvegarde
    prepareSaveData(slot) {
        const saveData = {
            ...gameState.state,
            meta: {
                ...gameState.state.meta,
                saveSlot: slot,
                lastSaved: new Date().toISOString(),
                gameVersion: "1.0.0",
                saveVersion: "1.0"
            }
        };

        // Ajouter des checksums pour vérifier l'intégrité
        saveData.checksum = this.calculateChecksum(saveData);
        
        return saveData;
    }

    // Charge une sauvegarde depuis un slot
    loadGame(slot = 1) {
        if (slot < 1 || slot > this.maxSaveSlots) {
            console.error(`Slot de chargement invalide: ${slot}`);
            return false;
        }

        try {
            const rawData = localStorage.getItem(`shinobi_save_${slot}`);
            
            if (!rawData) {
                console.log(`Aucune sauvegarde trouvée dans le slot ${slot}`);
                return false;
            }

            // Décompresser si nécessaire
            const saveData = this.isCompressed(rawData) ? 
                this.decompressData(rawData) : JSON.parse(rawData);

            // Vérifier l'intégrité
            if (!this.verifySaveIntegrity(saveData)) {
                console.error("Sauvegarde corrompue détectée!");
                this.showSaveNotification("Sauvegarde corrompue!", "error");
                return false;
            }

            // Vérifier la compatibilité des versions
            if (!this.checkVersionCompatibility(saveData)) {
                console.warn("Version de sauvegarde incompatible, migration nécessaire");
                saveData = this.migrateSaveData(saveData);
            }

            // Charger les données dans gameState
            gameState.state = saveData;
            
            this.showSaveNotification(`Jeu chargé (Slot ${slot})`);
            console.log(`Chargement réussi depuis le slot ${slot}`);
            return true;

        } catch (error) {
            console.error("Erreur lors du chargement:", error);
            this.showSaveNotification("Erreur de chargement!", "error");
            return false;
        }
    }

    // Obtient les informations de toutes les sauvegardes
    getSaveSlotInfo() {
        const slotsInfo = [];
        
        for (let slot = 1; slot <= this.maxSaveSlots; slot++) {
            const info = this.getSingleSlotInfo(slot);
            slotsInfo.push(info);
        }
        
        return slotsInfo;
    }

    // Obtient les informations d'un slot spécifique
    getSingleSlotInfo(slot) {
        try {
            const rawData = localStorage.getItem(`shinobi_save_${slot}`);
            
            if (!rawData) {
                return {
                    slot: slot,
                    exists: false,
                    empty: true
                };
            }

            const saveData = this.isCompressed(rawData) ? 
                this.decompressData(rawData) : JSON.parse(rawData);

            return {
                slot: slot,
                exists: true,
                empty: false,
                playerName: saveData.player.name || "Ninja Sans Nom",
                level: saveData.player.level || 1,
                clan: saveData.player.clan || "Aucun",
                region: saveData.world.currentMap || "konoha_forest",
                lastSaved: saveData.meta.lastSaved || "Inconnu",
                playTime: saveData.progress.totalPlayTime || 0,
                saveVersion: saveData.meta.saveVersion || "1.0"
            };

        } catch (error) {
            console.error(`Erreur lors de la lecture du slot ${slot}:`, error);
            return {
                slot: slot,
                exists: true,
                empty: false,
                corrupted: true
            };
        }
    }

    // Supprime une sauvegarde
    deleteSave(slot) {
        if (slot < 1 || slot > this.maxSaveSlots) {
            console.error(`Slot de suppression invalide: ${slot}`);
            return false;
        }

        try {
            localStorage.removeItem(`shinobi_save_${slot}`);
            this.showSaveNotification(`Sauvegarde ${slot} supprimée`);
            return true;
        } catch (error) {
            console.error("Erreur lors de la suppression:", error);
            return false;
        }
    }

    // Compression des données (simple base64 pour l'exemple)
    compressData(data) {
        const jsonString = JSON.stringify(data);
        return btoa(jsonString); // Base64 encoding
    }

    // Décompression des données
    decompressData(compressedData) {
        const jsonString = atob(compressedData); // Base64 decoding
        return JSON.parse(jsonString);
    }

    // Vérifie si les données sont compressées
    isCompressed(data) {
        try {
            // Tente de parser directement
            JSON.parse(data);
            return false;
        } catch {
            // Si ça échoue, c'est probablement compressé
            return true;
        }
    }

    // Calcule un checksum simple pour vérifier l'intégrité
    calculateChecksum(data) {
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Vérifie l'intégrité de la sauvegarde
    verifySaveIntegrity(saveData) {
        if (!saveData.checksum) return true; // Pas de checksum = ancienne version
        
        const originalChecksum = saveData.checksum;
        delete saveData.checksum;
        const calculatedChecksum = this.calculateChecksum(saveData);
        saveData.checksum = originalChecksum;
        
        return originalChecksum === calculatedChecksum;
    }

    // Vérifie la compatibilité des versions
    checkVersionCompatibility(saveData) {
        const saveVersion = saveData.meta?.saveVersion || "1.0";
        const currentVersion = "1.0";
        
        return saveVersion === currentVersion;
    }

    // Migration des données de sauvegarde
    migrateSaveData(saveData) {
        // Ici on pourrait ajouter la logique de migration
        // pour les futures versions du jeu
        console.log("Migration de sauvegarde effectuée");
        return saveData;
    }

    // Met à jour les métadonnées de sauvegarde
    updateSaveMetadata(slot) {
        gameState.state.meta.saveSlot = slot;
        gameState.state.meta.lastSaved = new Date().toISOString();
    }

    // Affiche une notification de sauvegarde
    showSaveNotification(message, type = "success") {
        // Cette fonction sera implémentée dans le système UI
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Créer une notification temporaire
        const notification = document.createElement('div');
        notification.className = `save-notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: ${type === 'error' ? '#e74c3c' : '#27ae60'};
            color: white;
            border-radius: 5px;
            z-index: 10000;
            transition: opacity 0.3s;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Export de sauvegarde pour partage/backup
    exportSave(slot) {
        const saveData = localStorage.getItem(`shinobi_save_${slot}`);
        if (saveData) {
            const blob = new Blob([saveData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `shinobi_save_${slot}_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    }

    // Import de sauvegarde
    importSave(file, slot) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const saveData = e.target.result;
                    localStorage.setItem(`shinobi_save_${slot}`, saveData);
                    resolve(true);
                } catch (error) {
                    reject(error);
                }
            };
            reader.readAsText(file);
        });
    }

    // Nettoyage à la fermeture
    cleanup() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
    }
}

// Instance globale du système de sauvegarde
const saveSystem = new SaveSystem();
