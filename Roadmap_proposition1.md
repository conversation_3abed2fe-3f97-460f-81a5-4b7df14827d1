Game Design Document: “Shadow of the Shinobi”

---

1. Overview

- Game Title (Working): Shadow of the Shinobi
- Genre: 2D Browser-based Action RPG (Side-scrolling, First-Person Perspective)
- Inspiration: Naruto universe (clone-inspired lore), classic 2D exploration & progression games
- Target Audience: Teens and adults who enjoy ninja-themed RPGs, anime-inspired art, and progression-driven gameplay
- Platform: HTML5/Javascript (deployable in modern browsers)

---

2. High-Level Concept

- Premise: Start as a Level 1 ninja trainee in the hidden village of Konoha. Navigate a vast, initially hidden world in a 2D side-scrolling first-person view, consuming “Chakra Energy” to advance. Discover villages, dungeons, and enemies to defeat. Collect powerful items and skills to evolve your ninja rank, face progressively harder foes, and uncover an original clan-centric lore reminiscent of Naruto.
- Core Loop:
  1. Explore: Unveil the map bit by bit by spending Chakra Energy.
  2. Encounter: Random encounters include enemy ninjas, wild beasts, treasure chests, NPCs, and mini-dungeons.
  3. Battle Loot & Grind: Win fights to gain Experience (EXP), currency (Ryo), and potential loot (scrolls, weapons, relics).
  4. Upgrade: Spend EXP to unlock new skills on a ninja skill tree, upgrade equipment, or buy Chakra-Restoring items.
  5. Progress: When you reach a target EXP threshold, rank up (e.g., Genin → Chūnin → Jōnin), unlocking new regions (e.g., Forest of Whispers → Hidden Sand Outpost → Mountain Shrine).
  6. Repeat: Continue exploring higher-tier maps with tougher enemies, richer lore, and unique dungeons.

---

3. Story & Lore

1. World Background:
   - The “Land of Leaves” is composed of multiple hidden villages (Konoha, Sand, Mist, Stone), each led by a Daimyō and supported by a hidden council of elder shinobi. Centuries ago, the Shinobi Clans of these villages formed alliances to keep peace.
   - A mysterious organization called “Kage’s Shadows” seeks forbidden jutsu artifacts scattered across the land to resurrect an ancient warlord.

2. Player Backstory:
   - The player is the orphaned child of a legendary Konoha clan that was wiped out by Kage’s Shadows. Taken in by the academy, you graduate as a Level 1 “Genin” and vow to rebuild your clan’s honor.

3. Major Factions/Clans:
   - Clan Ryūjin (Player’s Clan): Mastery of dragon-inspired jutsu, resilient body techniques.
   - Clan Yūrei: Ghost-inspired stealth clan from the Land of Mist.
   - Clan Kaze: Wind-style specialists from the Sand villages.
   - Kage’s Shadows: Rogue group with Yin-Yō chakra experiments.

4. Plot Progression (Phase-Based):
   - Phase 1 (Konoha & Surrounding Forests):
     - Tutorial & Starting Quests: Learn basic movement, Chakra management, and combat.
     - Main Quest: Locate the “Scroll of Ryūjin” lost near the Forest of Whispers.
   - Phase 2 (Sand Village & Desert Wastes):
     - Rank-Up Requirement: Reach Chūnin (EXP ~1000).
     - Quests: Ally with Clan Kaze to retrieve Wind Scrolls to counter desert storms.
   - Phase 3 (Mist Village & Hidden Marshes):
     - Rank-Up Requirement: Jōnin (EXP ~3000).
     - Quests: Investigate “Phantom Nin” kidnappings, discover Fog Tactic manuals.
   - Phase 4 (Stone Village & Mountain Shrine):
     - Rank-Up Requirement: Elite Jōnin (EXP ~6000).
     - Final Arc: Confront Kage’s Shadows beneath the Mountain Shrine, stop the resurrection ritual.

---

4. Gameplay Mechanics

4.1. Movement & Exploration

- Perspective & Controls:
  - Strict 2D side-scroll with first-person depth clues (layered parallax backgrounds).
  - Keyboard (or on-screen buttons) to move left/right, jump, and toggle a mini-scan-mode (reveals hidden traps/loot within a short radius at the cost of Chakra).
- Chakra Energy System:
  - Max Chakra: Starts at 100 units.
  - Energy Cost: Each step forward consumes 1 Chakra unit. Entering special zones (dense forests, caves) costs 5 Chakra at once.
  - Rest & Recovery: Automatically regain 10 Chakra per second when standing still and not in combat. Items (Chakra Potion) instantly restore +50 Chakra.
  - Failure State: If Chakra hits 0 and the player is not at a safe checkpoint, the ninja faints, dropping back to the last safe location (village or shrine).

4.2. Combat System

- Battle Initiation:
  - Randomized when exploring a tile or triggered by stepping on an enemy’s “vision radius.”
  - Transitions into a battle screen (side-view) or a popup overlaid on the exploration screen.
- Battle Mechanics:
  - Basic Attacks:
    - Light Attack (Low Chakra cost, quick recovery)
    - Heavy Attack (Higher damage, higher Chakra cost, slow recovery)
  - Jutsu & Skills:
    - Unlockable via skill tree. Examples:
      - Dragon Slash (Ryūjin): Costs 20 Chakra, deals medium damage + burn effect.
      - Shadow Clone: Costs 30 Chakra, creates a clone for 10 seconds that deals 50% damage.
      - Sand Barrier (Kaze): Costs 25 Chakra, reduces incoming damage by 50% for 5 seconds.
  - Items in Battle:
    - Shuriken (10 Ryo each): Deals minor damage, interrupts enemy cast.
    - Chakra Potion: Restore 50 Chakra.
  - Enemy Behavior:
    - Diverse enemy “classes”:
      - Wild Beast: High HP, but predictable melee.
      - Rogue Ninja: Medium HP, uses basic jutsu (fireball, water whip).
      - Elite Shinobi: Stronger AI; can dodge, counterattack, and use multi-step combos.
  - Rewards:
    - EXP: Base on enemy level (e.g., Beast Lv 1 → 50 EXP).
    - Ryo (Currency): Chance drop (10–50 Ryo).
    - Loot: Scroll fragments (collect 5 to learn a new jutsu), weapon pieces (shinobi sword, kunai).

4.3. Progression & Leveling

- EXP Thresholds:
  - Level 1 → Level 2: 100 EXP
  - Level 2 → Level 3: 200 EXP (scales up exponentially)
  - Reaching specific key ranks (Chūnin at Level 5, Jōnin at Level 10, etc.) triggers new region access.
- Skill Tree:
  - 3 Branches:
    - Taijutsu (Melee mastery): Improves basic attack combos, unlocks “Flying Kick” or “Iron Fist.”
    - Ninjutsu (Elemental jutsu): Unlock elemental projectiles, area-of-effect skills.
    - Genjutsu (Illusions & Buffs): Stun enemies, create illusions to escape.
  - Skill Points (SP): Earn 1 SP per level. Some key-jutsu unlock require reaching specific rank and gathering scroll fragments.
- Equipment & Inventory:
  - Weapon Slots (Sword/Kunai), Armor Slot (Light/Medium/Heavy Shinobi Armor), Accessory (Necklace, Ring)
  - Inventory Capacity: Limited to 20 slots. Carrying a “Scroll Bag” expands capacity by +10.
  - Item Tiers: Common → Uncommon → Rare → Epic → Legendary. Higher-tier gear sometimes drops from boss or high-level dungeons.

4.4. Health & Recovery

- HP System:
  - Starting HP at Level 1: 100 HP. Scales +20 HP per level.
  - Health potions (Small: +30 HP; Large: +100 HP) can be used in and out of battle.
- Checkpoints & Fast Travel:
  - Villages, Hidden Treeshrines, and Dungeon Entrances serve as auto-save points.
  - Once discovered, you can fast travel to any active checkpoint from the village hub for a small Ryo fee.

---

5. World & Level Design

5.1. Overworld Map Structure

- Map Visibility:
  - Initially completely hidden (“fog of war”). As the player moves tile by tile, tiles reveal.
  - Each tile can contain:
    - Empty Path: No event.
    - Random Encounter Trigger: Battle or loot.
    - Landmarks/Points of Interest: NPCs, vendor stalls, side-quest markers.
    - Dungeon Entry: Leads into a mini-dungeon.

- Map Segmentation by Region & Difficulty:
  1. Konoha Forest (Level 1–3):
     - Terrain: Dense trees, shallow streams, bamboo groves.
     - Enemies: Wild boars, forest bandits (Lv 1–3), low-level ninja.
     - Collectibles: Beginner chakra scrolls, basic kunai.
  2. Forest of Whispers (Level 3–5):
     - Terrain: Fog-laden groves, hidden traps.
     - Enemies: Yūrei scouts (Lv 3–4), shadow beasts, vine serpents.
     - Dungeon: “Eyes of the Forest” – clear illusions to collect advanced scrolls.
  3. Sand Wastes & Outskirts (Level 5–8):
     - Terrain: Arid dunes, disused sand shrines.
     - Enemies: Sand ninjas (Kaze clan henchmen), scorpion beasts.
     - Dungeon: “Tomb of the Dune King” – environmental hazards (quicksand, sandstorms).
  4. Hidden Marshlands (Level 8–10):
     - Terrain: Poisonous swamps, decaying bridges, mist.
     - Enemies: Mist genjutsu specialists, swamp sirens.
     - Dungeon: “Phantom Keep” – spectral illusions, phasing enemies.
  5. Mountain Shrine (Level 10+):
     - Terrain: Rocky cliffs, temples in the sky.
     - Boss Encounters: High-ranking members of Kage’s Shadows (Lv 10–12).

5.2. Villages & Hubs

- Konoha Village:
  - Layout: Centrally located hub; houses the Academy, Weapon Shop, Inn, Chakra Shrine.
  - NPCs: Sensei (tutorials), Weapon Blacksmith, Potion Merchant, Mission Board.
  - Available Quests:
    - “Forest Patrol”: Help clear bandits (Reward: 100 Ryo, 50 EXP, Basic Kunai).
    - “Scroll Patrol”: Find 3 beginner chakra scrolls in the Whispering Woods (Reward: 1 Skill Point).
- Sand Outpost:
  - Vendor selling Sand-Style scrolls, special desert-resistant armor.
  - Side quests: “Quench the Oasis” – restore water supply to villagers (Reward: Rare Potion).
- Mist Dock:
  - Ship travel to Mist Village.
  - Vendor sells stealth gear, Genjutsu scroll fragments.
- Mountain Shrine Keep:
  - Final endgame hub.
  - Sage NPC who bestows final Jōnin trials.

5.3. Dungeons & Special Areas

- “Eyes of the Forest” Dungeon (Konoha Border):
  - Layout: 5 rooms. Include illusion-triggered walls; players must use “Scan” or Genjutsu to reveal.
  - Mini-Boss: Forest Guardian (Lv 5): Resist fire & water jutsu.
  - Loot: Mid-Tier Rare scroll fragments, Epic bow.
- “Tomb of the Dune King” (Sand Wastes):
  - Hazards: Quicksand pits (slow movement, minor Chakra drain), random sandstorm events (darkens screen, random damage over time).
  - Boss: Dune King (Lv 7): Uses Sand Barrier & Sandstorm jutsu.
  - Loot: Legendary Sand-Style scroll, Desert Armor (rare).
- “Phantom Keep” (Marshlands):
  - Puzzle: Alter light beams to dispel fog illusions.
  - Boss: Mist Shinobi (Lv 9): Teleports, inflicts poison.
  - Loot: Epic Genjutsu scroll, Poison-Resistant Tabi Boots.

---

6. Progression & Progress Unlocks

- Rank Milestones:
  - Genin (Level 1–4): Access to Konoha Forest, basic jutsu.
  - Chūnin (Level 5–7): Access to Sand Wastes, unlock “Ninja Tools” (explosive tags, smoke bombs).
  - Jōnin (Level 8–10): Access to Marshlands, mid-tier jutsu, ability to craft low-tier weapons.
  - Elite Jōnin (Level 10+): Access to Mountain Shrine, final jutsu tier, purchase Legendary gear.

- Skill Tree Branch Unlocks:
  - Level 3: Unlock second-tier Taijutsu skill (“Cyclone Kick”).
  - Level 5: Unlock second-tier Ninjutsu (“Wind Cutter”).
  - Level 7: Unlock Genjutsu (“Mirage Illusion” to confuse enemies).
  - Level 10: Gain access to a special “Bloodline” skill (Ryūjin’s “Dragon’s roar” AoE).

- Item & Crafting Progression:
  - Basic Materials (Lv 1–4): Collect wood & stones to craft Simple Kunai.
  - Mid Materials (Lv 5–7): Sand-crystal shards (in desert) to forge Sand Kunai (inflicts bleed).
  - Elite Materials (Lv 8–10): Ghost lotus petals (marsh), tempered steel (mountain) to craft Legendary Ninja Sword.

---

7. User Interface (UI) & Heads-Up Display (HUD)

- HUD Elements (Exploration Mode):
  - Top Left: Mini-map (uncovered area only), with icons for NPCs, landmarks.
  - Bottom Left: HP Bar (green), Chakra Bar (blue). Numeric display above bars (e.g., “HP 120/120”).
  - Bottom Right: Current Equipped Weapon & Quick-Slots for Items (1–4 keys).
  - Top Right: Current Level & EXP progress bar (progress to next level).
  - Center: Subtle translucent energy cost indicator when moving (flashes Chakra icon with “–1”).

- Battle Screen UI:
  - Left Panel (Player): HP/Chakra bars, small avatar icon.
  - Right Panel (Enemy): Enemy’s HP bar, name, level.
  - Bottom Center: Action Menu (Attack, Jutsu, Item, Escape).
  - Portrait Popups: When Skills/Jutsu are used, show a small animated portrait.

- Menus & Overlays:
  - Main Menu (Esc Key): Inventory, Skill Tree, Map, Settings, Quit.
  - Inventory Screen: Grid display of items, with tooltip on hover (name, description, effect).
  - Skill Tree Screen: Node-based tree; unlocked nodes clickable to spend SP; unavailable nodes greyed out with “Req: Lv X.”
  - Map Screen: Shows full map with revealed tiles; clickable to fast-travel to visited checkpoints (if in village hub).
  - Quest Log: Lists active quests with objectives & markers on the mini-map.

---

8. Art & Audio Direction

8.1. Art Style

- Characters & Environments:
  - 2D Pixel-Art Style: High resolution ~32×32 pixel sprites for characters, 16-bit aesthetic backgrounds.
  - Animations:
    - Movement: Walk, run, jump, crouch (4–6 frames).
    - Combat: Idle stance, light attack animation (3 frames), heavy attack (5 frames), jutsu charge & release (8 frames).
    - Enemy Types: Distinct color palettes. Sand ninjas wear desert camo, mist ninjas in dark teal cloaks.
  - Environment Tilesets:
    - Konoha Forest: Vibrant greens, simplified textures for leaves & trunks.
    - Sand Wastes: Warm tans/oranges, cracked ground, occasional tumbleweed.
    - Marshlands: Dark greens, purples, fog overlays.

8.2. Audio

- Background Music:
  - Village Theme (Konoha): Gentle taiko drums, bamboo flute motifs.
  - Forest Theme: Soft percussion, wind chimes, ambient bird calls.
  - Desert Theme: Sparse strings, distant drumbeats, ambient wind sounds.
  - Final Shrine Theme: Tense strings, chanting vocals, deep taiko pulses.
- Sound Effects:
  - Movement: Footstep swishes (wood, sand, swamp).
  - Combat:
    - Sword slashes (metallic WHOOSH).
    - Shuriken throw (subtle twinkle + whoosh).
    - Jutsu cast (chakra charging hum, elemental impact).
  - UI Feedback: Button clicks, menu open/close chime, level-up fanfare.
- Voice Overs (Optional):
  - Short spoken phrases in Japanese with English subtitles (“Ganbatte!” when leveling up, “Kage Bunshin!” when cloning).

---

9. Systems & Technical Details

9.1. Core Technology Stack

- Front End:
  - HTML5 Canvas for rendering (side-scroller engine).
  - Javascript Framework: Phaser.js (v3.x) or equivalent 2D game engine for entity management, physics, tilemaps.
  - Asset Loading: JSON-based tilemap files, sprite sheets (PNG).
  - State Management: Use a simple Singleton GameState object to store player stats, inventory, world-unlocked flags.

- Back End (Optional for Browser-Only):
  - Local Storage or IndexedDB: Save game progress (player inventory, level, unlocked areas).
  - Server-Side (Optional for Multiplayer/Cloud-Save): Node.js + Express + MongoDB (store user accounts, leaderboards, achievements).

9.2. Level & Data Files

- World Map Data:
  - JSON files containing tile IDs, event triggers (enemy spawns, item spawns, NPC positions).
  - Per-region file: e.g., konoha_forest.json, sand_wastes.json.
- NPC & Quest Data:
  - JSON objects: NPC_ID, Name, Position (x, y), Dialogue Array, Quest_ID(s).
  - Quest definitions: Quest_ID, Title, Description, Objectives (kill X enemies, find Y items), Rewards.
- Enemy & Loot Tables:
  - For each region: list of enemy types, spawn probabilities, level ranges, loot tables with drop rates.
  - Boss definitions: stats, AI patterns, drop table.

9.3. Control & Input

- Keyboard (Desktop):
  - Movement: Arrow keys or A/D for left/right, W or ↑ for jump.
  - Combat: J (Light Attack), K (Heavy Attack), U (Open Skill Menu), I (Inventory), L (Map), Space (Use Primary Item).
  - Pause/Menu: Esc.
- Touch (Mobile):
  - On-screen arrow controls for movement.
  - Buttons for Light/Heavy Attack, Jutsu Menu, Item.
  - Tap top corner to open main menu.

9.4. Save/Load System

- Auto-Save Triggers:
  - Entering/exiting villages, completing main quest objectives, leveling up.
  - Manual save at “Chakra Shrines” found every few tiles deeper into each zone.
- Save Data Structure (localStorage JSON):
{
  "player": {
    "level": 3,
    "EXP": 150,
    "HP": 120,
    "MaxHP": 140,
    "Chakra": 60,
    "MaxChakra": 100,
    "inventory": [ { "item_id": "chakra_potion", "qty": 2 }, ... ],
    "equipment": { "weapon": "basic_ninja_sword", "armor": "leather_armor" },
    "skills": [ "dragon_slash", "shadow_clone" ],
    "location": { "region": "Forest of Whispers", "tile_x": 12, "tile_y": 5 }
  },
  "world": {
    "discovered_tiles": [ /* array of boolean flags per tile */ ],
    "completed_quests": [ "scroll_patrol" ],
    "unlocked_regions": [ "Konoha", "Forest of Whispers" ]
  }
}

---

10. Monetization (If Applicable)

(Optional: If the game will include light monetization or optional in-game purchases.)

- Cosmetic Skins:
  - Buy new ninja outfits (different color palettes, headbands).
- Premium Scroll Packs:
  - One-time purchase to instantly unlock a random mid-tier Ninjutsu or Genjutsu scroll (ensure game balance—avoid pay-to-win).
- Ad-Based Energy Top-Up:
  - Watch a short ad to refill 50 Chakra once every hour.

---

11. Progression Timeline & Milestones

1. Prototype (Month 1–2):
   - Core movement & exploration engine.
   - Basic tilemap display with fog-of-war reveal.
   - Simple battle system (one enemy type, basic attack).

2. Alpha (Month 3–4):
   - Implement Chakra system, HP system, and basic inventory.
   - Create 2 regions (Konoha Forest & Forest of Whispers).
   - Add skill tree with 3–4 skills.
   - Design 1 mini-dungeon (“Eyes of the Forest”).

3. Beta (Month 5–7):
   - Expand world to include Sand Wastes and Desert Dungeon.
   - Polished combat: multiple enemy types, jutsu animations.
   - UI refinement (HUD, menus, map).
   - Full quest system with 5 main quests, 10 side quests.

4. Release Candidate (Month 8–9):
   - Final region (Mist Marshlands) + Mountain Shrine boss.
   - Audio integration: BGM for each region, combat SFX.
   - Polish art assets, fix bugs, optimize performance.
   - Closed-beta testing & feedback iteration.

5. Launch (Month 10):
   - Public release on web portal.
   - Marketing: social media teasers, playable demo.
   - Post-Launch: Monitor analytics, issue updates (balance patches, new quests).

---

12. Glossary of Key Terms

- Chakra Energy: Resource consumed for movement and jutsu.
- Genin/Chūnin/Jōnin: Ninja ranks (levels) that gate progression.
- Jutsu: Special ninja techniques (Ninjutsu = elemental attacks; Genjutsu = illusions; Taijutsu = physical attacks).
- Ryo: In-game currency used to purchase items, equipment, or fast travel.
- Fog of War: The mechanic that keeps unexplored map areas hidden until visited.
- Skill Point (SP): Points earned on level up to unlock skills in the skill tree.
- Dungeon: Self-contained area with multiple rooms, puzzles, and a boss.

---

13. Risk Analysis & Mitigation

1. Player Frustration Over Chakra Drain:
   - Mitigation: Ensure enough Chakra pickups spawn early on. Add “safe resting spots” near start. Provide tutorials about Chakra management.

2. Over-Complicated Skill Tree:
   - Mitigation: Limit initial skill tree branches to 3–4 skills, reveal advanced branches only after Level 5.

3. Balancing Difficulty Spikes in Dungeons:
   - Mitigation: Playtest phases carefully. Provide clear level recommendations before entering a dungeon (“Recommended Lv 4–5”).

4. Performance on Low-End Devices:
   - Mitigation: Optimize assets (sprite sheet packing, tilemap chunking), offer graphics-quality toggle (low/high).

---

14. Final AI Builder Prompt

“Build a 2D browser-based Ninja RPG game titled ‘Shadow of the Shinobi’ using Phaser.js or a comparable HTML5 game engine.

Overview: The player begins as a Level 1 Genin in the hidden Leaf Village (Konoha). The world map (side-scrolling, first-person 2D view) is initially hidden by a fog of war. Moving forward consumes Chakra Energy (1 unit per tile; 5 units in hazardous terrain). Resting regains Chakra. If Chakra reaches zero away from a safe checkpoint, the player is returned to the last checkpoint.

Exploration & Map:
- Reveal tiles as the player moves. Each tile may trigger random encounters (battles, loot, NPCs, dungeon entries).
- Regions unlock as the player levels up and reaches certain ninja ranks:
  1. Konoha Forest (Lv 1–3)
  2. Forest of Whispers (Lv 3–5)
  3. Sand Wastes & Tomb Dungeon (Lv 5–8)
  4. Hidden Marshlands & Phantom Keep (Lv 8–10)
  5. Mountain Shrine & Kage’s Shadows Boss (Lv 10+)

Combat System:
- Side-view battle screen triggered upon encountering enemies.
- Player has basic light/heavy attacks, unlockable jutsu via a skill tree (Taijutsu, Ninjutsu, Genjutsu).
- Enemies range from wild beasts to rogue ninja to elite bosses. Battles award EXP, Ryo, and possible loot (scroll fragments, weapons).

Progression:
- EXP thresholds for levels (Genin to Chūnin to Jōnin, etc.). Each level grants 1 Skill Point (SP) to spend on the skill tree.
- Skill Tree: 3 branches (Taijutsu, Ninjutsu, Genjutsu) with unlock requirements (level, scroll fragments).
- Equipment slots: Weapon, Armor, Accessory. Items of increasing rarity (Common → Legendary). Crafting uses region-specific materials.

Villages & Hubs:
- Konoha Village: Tutorial hub with shop, quest board, Chakra Shrine (auto-save).
- Sand Outpost, Mist Dock, Mountain Shrine Keep: Each has unique merchants, side quests, trainers for specialized jutsu.

Dungeons & Bosses:
- “Eyes of the Forest” (Konoha region): illusion puzzles, mid-level boss.
- “Tomb of the Dune King” (Sand Wastes): environmental hazards, sandstorm boss.
- “Phantom Keep” (Marshlands): illusion-based puzzles, poison boss.
- “Mountain Shrine” (Final area): Multi-stage boss fight against Kage’s Shadows leader.

UI & HUD:
- Ultimate HUD shows HP, Chakra, Level/EXP, mini-map, equipped items. Battle screen with action menu (Attack, Jutsu, Item, Escape).
- Menus for Inventory, Skill Tree, Map (unlocked tiles displayed), Quest Log.

Art & Audio:
- Pixel-art sprites (~32×32 px) with smooth 6–8 frame animations.
- Tilesets that match each region’s aesthetic (lush forest, arid desert, misty swamp, rocky mountain).
- BGM for each region (village, exploration, combat) and SFX for attacks, jutsu, item use.

Save System:
- Auto-save at villages and Chakra Shrines. Manual save at checkpoints. Save data stored in localStorage with JSON structure (player stats, world progress).

Monetization (Optional):
- Cosmetic skins, premium scroll packs (balanced to avoid pay-to-win), optional ad-based Chakra top-ups.

Deliverables:
1. Fully functional HTML5/JS build playable in modern browsers.
2. Modular codebase organized into subfolders: assets/ (sprites, audio), scenes/ (each region + battle), systems/ (combat, inventory, save), data/ (JSON for maps, enemies, quests), and ui/ (HUD, menus).
3. Documentation for asset imports, JSON map formatting, and instructions to add new regions/dungeons.

Goals: Create an addictive progression loop—explore, battle, upgrade—wrapped in a lore-rich, Naruto-inspired world with original clans, jutsu, and a final confrontation against the rogue faction “Kage’s Shadows.”

---

15. Example Data Formats

To help the AI builder (or development team) get started quickly, here are sample JSON structures for various in‐game data files (map tiles, enemies, quests, items, and checkpoints).

15.1. Tilemap & Fog-of-War Data (e.g., konoha_forest.json)
{
  "region_name": "Konoha Forest",
  "tile_width": 32,
  "tile_height": 32,
  "map_dimensions": { "width": 100, "height": 50 },
  "tileset_image": "assets/tilesets/konoha_tiles.png",
  "layers": [
    {
      "name": "Ground",
      "data": [
        /* 2D array [row][col] with numeric tile IDs for ground textures */
      ]
    },
    {
      "name": "Objects",
      "data": [
        /* 2D array [row][col] with numeric tile IDs for objects/
           obstacles. 0 means empty. */
      ]
    }
  ],
  "fog_of_war": {
    "enabled": true,
    "initial_state": "full",     /* all tiles hidden at start */
    "reveal_radius": 3           /* number of tiles around player to reveal */
  },
  "events": [
    {
      "type": "enemy_spawn",
      "tile_x": 20,
      "tile_y": 10,
      "enemy_ids": ["forest_bandit", "wild_boar"],
      "spawn_probability": 0.2
    },
    {
      "type": "npc",
      "tile_x": 5,
      "tile_y": 3,
      "npc_id": "konoha_sensei"
    },
    {
      "type": "dungeon_entry",
      "tile_x": 35,
      "tile_y": 8,
      "dungeon_id": "eyes_of_the_forest"
    }
  ]
}

- Key Fields Explained
  - map_dimensions: Overall width/height in tiles.
  - fog_of_war.initial_state: Set to "full" so all tiles start blacked out; the game engine will progressively “reveal” as the player moves.
  - reveal_radius: Number of tiles in every direction (Manhattan distance) that become visible around the player.

15.2. Enemy Definitions (e.g., enemies.json)
{
  "forest_bandit": {
    "display_name": "Forest Bandit",
    "level": 2,
    "base_hp": 80,
    "base_chakra": 20,
    "attack": 10,
    "defense": 5,
    "jutsu": [
      {
        "id": "smoke_bomb",
        "name": "Smoke Bomb",
        "type": "ninjutsu",
        "chakra_cost": 10,
        "damage": 20,
        "effect": "Chance to blind player for 3s"
      }
    ],
    "loot_table": [
      { "item_id": "basic_kunai", "drop_rate": 0.25 },
      { "item_id": "bandit_cloak", "drop_rate": 0.05 }
    ],
    "spawn_regions": ["Konoha Forest", "Forest of Whispers"]
  },
  "wild_boar": {
    "display_name": "Wild Boar",
    "level": 1,
    "base_hp": 50,
    "attack": 8,
    "defense": 2,
    "jutsu": [],
    "loot_table": [
      { "item_id": "beast_meat", "drop_rate": 0.50 },
      { "item_id": "boar_tusk", "drop_rate": 0.10 }
    ],
    "spawn_regions": ["Konoha Forest"]
  },
  "forest_guardian": {
    "display_name": "Forest Guardian",
    "level": 5,
    "base_hp": 350,
    "base_chakra": 100,
    "attack": 25,
    "defense": 15,
    "jutsu": [
      {
        "id": "vine_whip",
        "name": "Vine Whip",
        "type": "ninjutsu",
        "chakra_cost": 20,
        "damage": 40,
        "effect": "Root player for 2s"
      }
    ],
    "loot_table": [
      { "item_id": "guardian_scroll_fragment", "drop_rate": 1.0 },
      { "item_id": "epic_bow", "drop_rate": 0.15 }
    ],
    "spawn_regions": ["Forest of Whispers"]
  }
}

- Notes
  - jutsu: Each enemy can have zero or more jutsu abilities with their own chakra_cost, damage, and optional effect.
  - loot_table: An array where higher-tier items have lower drop rates.

15.3. Quest Definitions (e.g., quests.json)
{
  "scroll_patrol": {
    "title": "Scroll Patrol",
    "description": "Find 3 Beginner Chakra Scrolls in the Forest of Whispers and return to Sensei.",
    "region": "Forest of Whispers",
    "objectives": [
      {
        "type": "collect_item",
        "item_id": "beginner_chakra_scroll",
        "quantity": 3
      }
    ],
    "rewards": {
      "exp": 100,
      "ryo": 50,
      "item_id": "skill_point",
      "quantity": 1
    },
    "prerequisites": ["forest_tutorial_completed"],
    "is_repeatable": false
  },
  "forest_patrol": {
    "title": "Forest Patrol",
    "description": "Help clear the bandits who have been raiding woodcutters in Konoha Forest.",
    "region": "Konoha Forest",
    "objectives": [
      {
        "type": "defeat_enemy",
        "enemy_id": "forest_bandit",
        "quantity": 5
      }
    ],
    "rewards": {
      "exp": 50,
      "ryo": 30,
      "item_id": "basic_kunai",
      "quantity": 1
    },
    "prerequisites": [],
    "is_repeatable": true
  }
}

- Key Fields
  - objectives: Each objective can be type: "collect_item", type: "defeat_enemy", or even type: "talk_to_npc".
  - prerequisites: Other quest IDs that must be completed first (e.g., "forest_tutorial_completed").

15.4. Item & Equipment Definitions (e.g., items.json)
{
  "basic_kunai": {
    "display_name": "Basic Kunai",
    "type": "weapon",
    "slot": "weapon",
    "damage": 15,
    "chakra_cost": 0,
    "weight": 1,
    "rarity": "common",
    "sell_price": 10
  },
  "bandit_cloak": {
    "display_name": "Bandit Cloak",
    "type": "armor",
    "slot": "armor",
    "defense": 5,
    "weight": 3,
    "rarity": "uncommon",
    "sell_price": 25
  },
  "guardian_scroll_fragment": {
    "display_name": "Guardian Scroll Fragment",
    "type": "crafting_material",
    "description": "Collect 5 fragments to assemble the Guardian Scroll.",
    "rarity": "rare",
    "sell_price": 100
  },
  "chakra_potion": {
    "display_name": "Chakra Potion",
    "type": "consumable",
    "effect": {
      "restore_chakra": 50
    },
    "weight": 0.5,
    "rarity": "common",
    "sell_price": 20
  }
}

- Notes
  - Consumables can have an effect object describing restoration values.
  - Crafting materials will be used by a crafting system (later phase).

15.5. Checkpoints & Save Points (e.g., checkpoints.json)
[
  {
    "checkpoint_id": "konoha_village_entrance",
    "display_name": "Konoha Village Gate",
    "tile_x": 2,
    "tile_y": 1,
    "region": "Konoha Forest",
    "type": "village",
    "fast_travel_cost": 10
  },
  {
    "checkpoint_id": "chakra_shrine_wood_clearing",
    "display_name": "Wood Clearing Chakra Shrine",
    "tile_x": 18,
    "tile_y": 8,
    "region": "Konoha Forest",
    "type": "shrine",
    "fast_travel_cost": 0   /* Shrines can be free to fast-travel once discovered */
  }
]

- Each checkpoint has coordinates, a type ("village" or "shrine"), and a fast-travel cost in Ryo.

---

16. Implementation Details & Code Snippets

Below are high‐level code sketches (using Phaser 3) to illustrate how core systems—fog of war, player movement, and battle transitions—might be implemented.

Note: These snippets assume you have a working Phaser 3 project with a Scene subclass for exploration.

16.1. Fog of War Implementation (Phaser 3)
class ExplorationScene extends Phaser.Scene {
  constructor() {
    super({ key: 'ExplorationScene' });
  }

  preload() {
    // Load tilemap JSON and tileset images
    this.load.tilemapTiledJSON('konohaForestMap', 'assets/maps/konoha_forest.json');
    this.load.image('konohaTiles', 'assets/tilesets/konoha_tiles.png');

    // Create a black “fog” texture to overlay
    this.textures.generate('fog', { data: ['0'], pixelWidth: 32, pixelHeight: 32 });
  }

  create() {
    // Create the tilemap and layers
    this.map = this.make.tilemap({ key: 'konohaForestMap' });
    const groundTiles = this.map.addTilesetImage('KonohaTiles', 'konohaTiles');
    this.groundLayer = this.map.createLayer('Ground', groundTiles, 0, 0);
    this.objectLayer = this.map.createLayer('Objects', groundTiles, 0, 0);

    // Create a RenderTexture to act as the fog-of-war overlay
    this.fogRenderTexture = this.add
      .renderTexture(0, 0, this.map.widthInPixels, this.map.heightInPixels)
      .setDepth(10);

    // Fill the entire renderTexture with black
    this.fogRenderTexture.fill(0x000000);

    // Initialize a grid to track which tiles are revealed
    this.revealed = Array(this.map.height).fill().map(() => Array(this.map.width).fill(false));

    // Place player sprite at spawn point (tile coordinates 2,1 for example)
    this.player = this.physics.add.sprite(2 * 32, 1 * 32, 'playerSprite');
    this.player.setCollideWorldBounds(true);

    // Reveal initial area around player
    this.revealTilesAroundPlayer();
  }

  update(time, delta) {
    this.handlePlayerMovement();
    this.revealTilesAroundPlayer();
  }

  handlePlayerMovement() {
    const cursors = this.input.keyboard.createCursorKeys();
    const speed = 100;
    this.player.body.setVelocity(0);

    if (cursors.left.isDown) {
      this.player.body.setVelocityX(-speed);
    } else if (cursors.right.isDown) {
      this.player.body.setVelocityX(speed);
    }

    if (cursors.up.isDown) {
      this.player.body.setVelocityY(-speed);
    } else if (cursors.down.isDown) {
      this.player.body.setVelocityY(speed);
    }

    // After moving, deduct Chakra—handled in a separate subsystem
  }

  revealTilesAroundPlayer() {
    const tileX = Math.floor(this.player.x / 32);
    const tileY = Math.floor(this.player.y / 32);
    const radius = 3; // Reveal radius

    for (let dy = -radius; dy <= radius; dy++) {
      for (let dx = -radius; dx <= radius; dx++) {
        const x = tileX + dx;
        const y = tileY + dy;
        if (
          x >= 0 &&
          y >= 0 &&
          x < this.map.width &&
          y < this.map.height &&
          !this.revealed[y][x]
        ) {
          this.revealed[y][x] = true;
          // Draw a transparent tile at the position on the fogRenderTexture
          const worldX = x * 32;
          const worldY = y * 32;
          // Clear a 32×32 square at (worldX, worldY)
          this.fogRenderTexture.erase('fog', worldX, worldY);
        }
      }
    }
  }
}

- Explanation
  1. We create a RenderTexture the same size as the map, fill it black, and place it above all map layers.
  2. As the player moves, revealTilesAroundPlayer() clears black squares around their tile position.
  3. A 2D boolean array this.revealed ensures each tile is only revealed once (avoiding redundant draw calls).

16.2. Chakra Energy & Movement Cost
class PlayerStats {
  constructor(scene) {
    this.scene = scene;
    this.maxChakra = 100;
    this.currentChakra = 100;
    this.chakraRegenRate = 10; // per second
    this.lastRegenTime = 0;
 
16.2. Chakra Energy & Movement Cost  

class PlayerStats {
  constructor(scene) {
    this.scene = scene;
    this.maxChakra = 100;
    this.currentChakra = 100;
    this.chakraRegenRate = 10; // per second
    this.lastRegenTime = 0;
  }

  spendChakra(amount) {
    if (this.currentChakra >= amount) {
      this.currentChakra -= amount;
      if (this.currentChakra < 0) this.currentChakra = 0;
      return true;
    }
    return false; // Not enough Chakra
  }

  regenChakra(time) {
    if (time - this.lastRegenTime > 1000) {
      // Regen happens every 1 second if not moving
      if (
        this.scene.player.body.velocity.x === 0 &&
        this.scene.player.body.velocity.y === 0
      ) {
        this.currentChakra = Math.min(
          this.currentChakra + this.chakraRegenRate,
          this.maxChakra
        );
      }
      this.lastRegenTime = time;
    }
  }
}

// In ExplorationScene.update():
update(time, delta) {
  this.handlePlayerMovement();
  this.playerStats.regenChakra(time);

  // If the player moved this frame, spend 1 Chakra per tile crossed
  if (
    this.player.body.velocity.x !== 0 ||
    this.player.body.velocity.y !== 0
  ) {
    const timeSinceLastStep = time - this.lastStepTime;
    const stepInterval = 200; // assume 200ms per tile walk animation
    if (timeSinceLastStep >= stepInterval) {
      const spent = this.playerStats.spendChakra(1);
      if (!spent) {
        // Chakra depleted—knock player back to last checkpoint
        this.handleChakraDepletion();
      } else {
        this.lastStepTime = time;
      }
    }
  }

  this.revealTilesAroundPlayer();
}

Art Asset List & Specifications

Below is a (non‐exhaustive) list of required sprite sheets, background layers, and UI icons. Each entry includes recommended dimensions and naming conventions.

Asset Name	Type	Size/Specs	Notes
player_spritesheet.png	Character Sprite	256×128 px (8 columns × 4 rows, each frame 32×32)	Animations: idle, walk (4 frames), jump (2 frames), attack (3 frames)
forest_bandit.png	Enemy Sprite	128×64 px (4×2, frames 32×32)	Animations: idle, attack
wild_boar.png	Enemy Sprite	64×64 px (2×2, frames 32×32)	Animations: run, pounce
tiles_konoha_forest.png	Tileset	512×512 px, 16×16 tiles (each tile 32×32 px)	Ground, trees, rocks, bushes
tiles_sand_wastes.png	Tileset	512×512 px, 16×16 tiles (32×32 px each)	Sand dunes, cacti, broken pillars
tiles_marshlands.png	Tileset	512×512 px, 16×16 tiles (32×32 px each)	Swamp water, reeds, mossy rocks
tiles_mountain_shrine.png	Tileset	512×512 px, 16×16 tiles (32×32 px each)	Cliff edges, temple floors, statues
ui_hud.png	UI SpriteSheet	256×128 px (8×4 frames, each 32×32)	Icons: HP bar, Chakra bar, EXP icon, mini-map background
inventory_icons.png	UI Icon Set	512×64 px (16×2 frames, each 32×32)	Individual item icons (potions, scrolls, weapons)
button_sprites.png	UI Buttons	160×32 px (5×1 frames, each 32×32)	Idle, hover, pressed states

Animation Frame Breakdown (Example: player_spritesheet.png)

Row 0 (Frames 0–3): Idle (each frame 32×32)

Row 1 (Frames 4–7): Walk cycle (4 frames)

Row 2 (Frames 8–9): Jump start/end (2 frames), Idle in air (next frames as needed)

Row 3 (Frames 12–14): Basic attack (3 frames), recovery frame

Audio Asset List & Specifications

Asset Name	Type	Format	Duration / Notes
bgm_konoha_village.ogg	BGM	OGG Vorbis	Loopable, ~1:20 (min)
bgm_forest_explore.ogg	BGM	OGG Vorbis	Loopable, ~1:45
bgm_desert_wastes.ogg	BGM	OGG Vorbis	Loopable, ~2:00
bgm_phantom_keep.ogg	BGM	OGG Vorbis	Loopable, ~1:50
bgm_mountain_shrine.ogg	BGM	OGG Vorbis	Loopable, ~2:10
sfx_attack_light.wav	SFX	WAV	~0.2 s, short whoosh
sfx_attack_heavy.wav	SFX	WAV	~0.4 s, heavier impact sound
sfx_jutsu_cast.wav	SFX	WAV	~0.5 s, channelling hum
sfx_jutsu_hit.wav	SFX	WAV	~0.3 s, elemental hit effect
sfx_pickup_item.wav	SFX	WAV	~0.1 s, pick‐up chime
sfx_level_up.ogg	Jingle	OGG Vorbis	~1.0 s, fanfare
sfx_menu_open.wav	SFX	WAV	~0.1 s, UI click
sfx_chakra_deplete.wav	SFX	WAV	~0.3 s, faint buzz
voice_level_up.wav	VO	WAV	“Ganbatte!” (optional)

Loop Points: All BGM should have built‐in loop cues (e.g., last 4 beats) so they can seamlessly repeat in‐game without noticeable jumps.

Testing & Quality Assurance

19.1. Unit & Integration Testing

Gameplay Mechanics

Fog-of-War Reveal:

Write unit tests to confirm that revealTilesAroundPlayer() sets exactly the correct tiles from hidden to revealed when the player moves.

Test corner cases (player at map edge).

Chakra Consumption & Regeneration:

Simulate continuous movement for X seconds; confirm Chakra decreases by expected amount.

Simulate standing still; confirm correct regen per second.

Battle Transitions:

Mock an enemy_spawn tile with a 100% spawn chance (for reliability); ensure initiateBattle() always triggers.

After a simulated “player_win” or “player_lose,” confirm exploration resumes and stats are updated or reset properly.

Data File Validation

Write a JSON Schema for enemies.json, items.json, quests.json, and checkpoints.json.

Implement a small Node.js script that parses each data file against its schema before allowing the game to run.

19.2. Playtesting Workflow

Internal Smoke Tests (Alpha Stage):

Developers run through basic flows: moving around Konoha Forest, encountering at least one battle, using Chakra Potions, returning to checkpoint.

Confirm no fatal crashes, no missing assets, and that the fog-of-war reveals properly.

Closed-Beta (Regions 1 & 2):

5–10 testers play only Konoha Forest and Forest of Whispers.

Collect feedback on:

Difficulty curve (e.g., is it too easy/hard to reach Chūnin at Lv 5?).

Intuitiveness of HUD and menus.

Any “soft locks” where players run out of Chakra with no way to return.

Iterate on: adjust Chakra costs, add more early-game Chakra pickups, tweak enemy spawn rates.

Extended Beta (Full Map, Regions 1–4):

Broaden to 20–30 testers. Provide a bug-reporting channel (Discord or Trello board) where players can submit:

Visual glitches (sprite clipping, fog-of-war artifacts).

Balance issues (e.g., Dungeon A is too punishing vs. recommended level).

UX complaints (e.g., menus are hard to navigate, lacking tooltips).

Release Candidate QA (All Regions, Final Boss):

Final run-through to validate endgame: Mountain Shrine, Kage’s Shadows Boss fight.

Verify that save/load works properly across all zones.

Check mobile browser performance (Android and iOS) to ensure acceptable framerate on mid‐range hardware.

Fix any critical bugs (gamebreaking crashes, corrupted save data) before public launch.

Future Expansion & Post-Launch Roadmap

Although the base game covers five regions and a final boss arc, here are ideas for post-launch content updates or DLC:

20.1. New Regions & Villages

Land of Clouds (New Region, Level 12–15):

Floating sky islands connected by rope bridges.

New enemy types: Tengu Ninjas, Cloud Serpents.

New clan: Clan Raijin (Lightning‐style jutsu).

Hidden Ice Village (Level 15–18):

Frost‐themed dungeons, slippery ice mechanics.

Boss: Ice Demon that freezes player for short durations.

20.2. Additional Game Modes

Time Attack Challenge:

Players enter an isolated dungeon and must defeat waves of enemies as fast as possible.

Leaderboard showing top completion times (requires optional back-end).

PvP Arena (Optional Premium Feature):

Two players duel in a small arena.

Rank ladder: Bronze → Silver → Gold divisions.

Matchmaking based on player rank and gear score.

20.3. Expanded Skill Trees & Bloodlines

Introduce Bloodline Abilities for rare “Ryūjin Legacy” players—e.g., “Dragon’s Temper” passive that increases fire‐jutsu damage by 10%.

Additional advanced jutsu nodes requiring rare crafting materials.

20.4. Seasonal Events & Limited-Time Quests

Festival in Konoha (Spring Event):

Special cherry‐blossom décor in Village hub.

Unique mini‐game (catch falling petals to earn festival tokens).

Exchange tokens for limited‐edition cosmetic masks.

Shadow Invasion (Autumn Event):

Waves of “Kage’s Shadows” foot soldiers invade all regions.

Players defend checkpoints on a shared global map; top defenders earn exclusive clan banners.

Final Notes & Next Steps

AI Builder Integration

Use the “Final AI Builder Prompt” (from Section 14) as the starting point.

Provide the AI builder with the sample data files (JSON) and code snippets to minimize ambiguity.

Confirm that the AI tool can produce a Phaser 3 project skeleton with the folder structure:

bash
Copier
Modifier
/assets
  /audio
  /images
  /tilesets
  /sprites
/data
  enemies.json
  items.json
  quests.json
  checkpoints.json
  konoha_forest.json
  sand_wastes.json
  … (other regions)
/scenes
  ExplorationScene.js
  BattleScene.js
  VillageScene.js
  … (etc.)
/systems
  FogOfWar.js
  ChakraSystem.js
  CombatSystem.js
  SaveLoadSystem.js
/ui
  HUD.js
  InventoryUI.js
  SkillTreeUI.js
  MapUI.js
index.html
main.js
The AI builder should scaffold the code stubs, import the JSON data, and set up basic scene transitions.

Version Control & Collaboration

Initialize a Git repository (e.g., GitHub or GitLab).

Commit the scaffolded Phaser project immediately.

Use branch/protect policies (e.g., “development” branch for ongoing work, “main” for stable releases).

Milestones Recap

M1 (End of Month 1): Movement, fog-of-war, Chakra mechanics.

M2 (End of Month 3): Two full regions (Konoha, Forest of Whispers), basic combat & inventory.

M3 (End of Month 5): Final map regions (Sand Wastes, Marshlands), three mini-dungeons, skill tree UI.

M4 (End of Month 7): Mountain Shrine, final boss, full audio integration, polish.

M5 (Launch): Public release, QA sign-off, cloud‐save implementation (optional).

Documentation & Tutorials

Write a README that details:

How to run locally (e.g., npm install → npm start).

How to add new regions: describe JSON schema, tilemap generation process.

How to import new assets: naming conventions & folder placement.

How to deploy to a static-hosting service (GitHub Pages, Netlify).