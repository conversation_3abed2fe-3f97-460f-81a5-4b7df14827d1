# 🔍 Debug Écran Noir - Shinobi Chronicles

## 🎯 Problème Actuel

**Symptômes** :
- ✅ Interface HTML/CSS visible (HUD, mini-carte, boutons)
- ✅ Logs Phaser normaux (pas d'erreur)
- ✅ Scène d'exploration initialisée
- ❌ Canvas Phaser reste noir (carte invisible)

## 🔧 Corrections Appliquées

### **1. Configuration Caméra**
```javascript
// ❌ AVANT: Zoom trop élevé
this.cameras.main.setZoom(2);

// ✅ APRÈS: Zoom normal
this.cameras.main.setZoom(1);
```

### **2. Couleurs des Tuiles Plus Vives**
```javascript
// ✅ Couleurs améliorées pour visibilité
case 'village': tint = 0xffff00; // Jaune vif
case 'forest': tint = 0x00ff00;  // Vert vif
case 'water': tint = 0x0080ff;   // Bleu vif
```

### **3. Fond de Scène Visible**
```javascript
// ✅ Ajout d'un fond coloré
this.add.rectangle(0, 0, this.mapWidth * this.tileSize, this.mapHeight * this.tileSize, 0x2c3e50);
```

### **4. Fog of War Temporairement Désactivé**
```javascript
// ✅ Désactivé pour debug
// this.initializeFogOfWar();
```

### **5. Test de Visibilité Ajouté**
```javascript
// ✅ Éléments de test pour vérifier Phaser
createVisibilityTest() {
    const testRect = this.add.rectangle(width/2, height/2, 100, 100, 0xff0000);
    const testText = this.add.text(width/2, height/2 - 150, 'TEST VISIBILITÉ', {
        fontSize: '24px',
        color: '#ffffff',
        backgroundColor: '#000000'
    });
}
```

## 🧪 Tests à Effectuer

### **Test 1: Visibilité Phaser**
**Attendu** : Rectangle rouge + texte "TEST VISIBILITÉ" au centre
- ✅ **Si visible** → Phaser fonctionne, problème dans la génération de carte
- ❌ **Si invisible** → Problème de configuration Phaser

### **Test 2: Logs de Debug**
**Vérifier dans la console** :
```
🌍 Monde initialisé: 1600x1600 pixels
🟩 Tuile créée: (0,0) type=grass texture=grass_tile
📷 Caméra configurée: zoom=1, bounds=1600x1600
👤 Joueur créé à la position (25, 25)
🔍 Test de visibilité créé
```

### **Test 3: Position du Joueur**
**Vérifier** : Le sprite du joueur (clan Yūrei = shinobi_sprite violet)
- Position attendue : (25, 25) en tuiles = (800, 800) en pixels

## 🔍 Causes Possibles

### **A. Problème de Rendu**
- Textures générées incorrectement
- Profondeur (depth) des éléments
- Container du monde mal positionné

### **B. Problème de Caméra**
- Caméra mal positionnée
- Limites de caméra incorrectes
- Zoom ou bounds problématiques

### **C. Problème de Fog of War**
- Fog cache toute la carte
- Révélation initiale échoue
- Texture fog mal générée

### **D. Problème de Sprites**
- Sprites ninja non chargés
- Fallback vers textures générées
- Erreur de chargement d'assets

## 🛠️ Solutions Supplémentaires

### **Si Test de Visibilité Échoue**
```javascript
// Vérifier la configuration Phaser de base
console.log('Phaser version:', Phaser.VERSION);
console.log('Renderer:', this.renderer.type);
console.log('Canvas size:', this.cameras.main.width, 'x', this.cameras.main.height);
```

### **Si Carte Invisible Mais Test Visible**
```javascript
// Forcer la visibilité des tuiles
createTile(x, y, tileType) {
    const tile = this.add.rectangle(
        x * this.tileSize + this.tileSize/2,
        y * this.tileSize + this.tileSize/2,
        this.tileSize,
        this.tileSize,
        0x00ff00 // Vert vif
    );
    tile.setDepth(0);
}
```

### **Si Joueur Invisible**
```javascript
// Créer un joueur de test simple
this.player = this.add.circle(
    startPos.x * this.tileSize + this.tileSize/2,
    startPos.y * this.tileSize + this.tileSize/2,
    16,
    0xff0000 // Rouge vif
);
```

## 📊 Informations de Debug

### **Configuration Actuelle**
- **Taille carte** : 50x50 tuiles (1600x1600 pixels)
- **Taille tuile** : 32x32 pixels
- **Position joueur** : (25, 25) tuiles
- **Zoom caméra** : 1.0
- **Fog of war** : Désactivé temporairement

### **Sprites Disponibles**
- `shinobi_sprite` (clan Yūrei)
- `fighter_sprite` (clan Kaze)
- `samurai_sprite` (clan Ryūjin)
- Fallback : textures générées procéduralement

## 🎯 Prochaines Étapes

1. **Tester** avec les corrections appliquées
2. **Vérifier** la visibilité du test rouge
3. **Analyser** les logs de debug
4. **Identifier** la cause exacte
5. **Appliquer** la solution appropriée

## 💡 Notes Importantes

- L'interface HTML fonctionne → Problème isolé au canvas Phaser
- Pas d'erreur JavaScript → Configuration probablement correcte
- Logs normaux → Systèmes s'initialisent bien
- Problème probablement visuel/rendu

**Le test de visibilité va nous dire exactement où est le problème ! 🔍**
