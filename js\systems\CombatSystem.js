/**
 * Système de combat pour Shinobi Chronicles
 * Combine le système tour par tour de la Proposition 2 avec la profondeur de la Proposition 1
 */

class CombatSystem {
    constructor() {
        this.currentBattle = null;
        this.battleState = 'idle'; // idle, player_turn, enemy_turn, victory, defeat
        this.turnQueue = [];
        this.battleLog = [];
        this.combatEffects = [];
        
        this.initializeCombatData();
    }

    // Initialise les données de combat
    initializeCombatData() {
        this.statusEffects = {
            poison: { duration: 3, damagePerTurn: 5 },
            burn: { duration: 2, damagePerTurn: 8 },
            paralysis: { duration: 2, skipTurnChance: 0.5 },
            confusion: { duration: 3, wrongTargetChance: 0.3 },
            sleep: { duration: 2, skipTurn: true },
            boost_attack: { duration: 3, multiplier: 1.5 },
            boost_defense: { duration: 3, multiplier: 1.3 }
        };
    }

    // Démarre un combat
    startBattle(enemyData, battleType = 'random') {
        console.log(`Début du combat contre ${enemyData.name}`);
        
        this.currentBattle = {
            enemy: this.createEnemyInstance(enemyData),
            player: this.createPlayerInstance(),
            type: battleType,
            turn: 1,
            playerStatusEffects: [],
            enemyStatusEffects: [],
            battleStartTime: Date.now()
        };
        
        this.battleState = 'player_turn';
        this.battleLog = [];
        
        // Calculer l'ordre des tours basé sur la vitesse
        this.calculateTurnOrder();
        
        // Déclencher l'événement de début de combat
        this.onBattleStart();
        
        return this.currentBattle;
    }

    // Crée une instance d'ennemi pour le combat
    createEnemyInstance(enemyData) {
        return {
            ...enemyData,
            currentHp: enemyData.hp,
            maxHp: enemyData.hp,
            currentChakra: enemyData.chakra || 0,
            maxChakra: enemyData.chakra || 0,
            statusEffects: [],
            lastAction: null
        };
    }

    // Crée une instance du joueur pour le combat
    createPlayerInstance() {
        const player = gameState.player;
        return {
            name: player.name || "Ninja",
            level: player.level,
            hp: player.hp,
            maxHp: player.maxHp,
            chakra: player.chakra,
            maxChakra: player.maxChakra,
            attack: player.stats.attack,
            defense: player.stats.defense,
            speed: player.stats.speed,
            element: this.getClanElement(player.clan),
            statusEffects: [],
            lastAction: null
        };
    }

    // Calcule l'ordre des tours
    calculateTurnOrder() {
        const playerSpeed = this.currentBattle.player.speed;
        const enemySpeed = this.currentBattle.enemy.speed || 5;
        
        // Le plus rapide commence
        this.battleState = playerSpeed >= enemySpeed ? 'player_turn' : 'enemy_turn';
    }

    // Exécute une attaque du joueur
    executePlayerAction(actionType, actionData = {}) {
        if (this.battleState !== 'player_turn' || !this.currentBattle) {
            return false;
        }

        let result = null;

        switch (actionType) {
            case 'attack':
                result = this.executeAttack(this.currentBattle.player, this.currentBattle.enemy, actionData);
                break;
            case 'jutsu':
                result = this.executeJutsu(this.currentBattle.player, this.currentBattle.enemy, actionData);
                break;
            case 'item':
                result = this.useItem(actionData.itemId);
                break;
            case 'flee':
                result = this.attemptFlee();
                break;
            default:
                console.error(`Action inconnue: ${actionType}`);
                return false;
        }

        if (result) {
            this.addToBattleLog(result.message);
            
            // Vérifier si l'ennemi est vaincu
            if (this.currentBattle.enemy.currentHp <= 0) {
                this.endBattle('victory');
                return result;
            }
            
            // Passer au tour de l'ennemi
            this.battleState = 'enemy_turn';
            setTimeout(() => this.executeEnemyTurn(), 1500);
        }

        return result;
    }

    // Exécute une attaque basique
    executeAttack(attacker, defender, actionData) {
        const attackType = actionData.type || 'light';
        const chakraCost = 5; // Coût de base pour les attaques spéciales
        
        // Vérifier le chakra pour les attaques spéciales
        if (attackType !== 'light' && attacker.chakra < chakraCost) {
            return { success: false, message: "Pas assez de chakra!" };
        }

        // Calculer les dégâts
        let baseDamage = attacker.attack;
        let damageMultiplier = 1;

        switch (attackType) {
            case 'light':
                damageMultiplier = 0.8;
                break;
            case 'heavy':
                damageMultiplier = 1.3;
                attacker.chakra -= chakraCost;
                break;
            case 'combo':
                damageMultiplier = 1.1;
                attacker.chakra -= chakraCost * 0.5;
                break;
        }

        const finalDamage = this.calculateDamage(baseDamage * damageMultiplier, defender.defense);
        
        // Appliquer les dégâts
        defender.currentHp = Math.max(0, defender.currentHp - finalDamage);
        
        // Mettre à jour le chakra du joueur dans gameState
        if (attacker === this.currentBattle.player) {
            gameState.player.chakra = attacker.chakra;
        }

        return {
            success: true,
            damage: finalDamage,
            message: `${attacker.name} attaque ${defender.name} pour ${finalDamage} dégâts!`,
            actionType: 'attack'
        };
    }

    // Exécute un jutsu
    executeJutsu(attacker, defender, jutsuData) {
        const jutsu = this.getJutsuData(jutsuData.jutsuId);
        
        if (!jutsu) {
            return { success: false, message: "Jutsu inconnu!" };
        }

        // Vérifier le chakra
        if (attacker.chakra < jutsu.chakraCost) {
            return { success: false, message: "Pas assez de chakra pour ce jutsu!" };
        }

        // Dépenser le chakra
        attacker.chakra -= jutsu.chakraCost;
        
        // Mettre à jour le chakra du joueur dans gameState
        if (attacker === this.currentBattle.player) {
            gameState.player.chakra = attacker.chakra;
        }

        let result = { success: true, message: "", actionType: 'jutsu' };

        switch (jutsu.type) {
            case 'damage':
                result = this.executeOffensiveJutsu(attacker, defender, jutsu);
                break;
            case 'heal':
                result = this.executeHealingJutsu(attacker, jutsu);
                break;
            case 'buff':
                result = this.executeBuffJutsu(attacker, jutsu);
                break;
            case 'debuff':
                result = this.executeDebuffJutsu(defender, jutsu);
                break;
        }

        return result;
    }

    // Exécute un jutsu offensif
    executeOffensiveJutsu(attacker, defender, jutsu) {
        let baseDamage = jutsu.baseDamage + (attacker.attack * jutsu.attackMultiplier);
        
        // Appliquer les multiplicateurs élémentaires
        const elementMultiplier = this.getElementMultiplier(jutsu.element, defender.element);
        baseDamage *= elementMultiplier;
        
        const finalDamage = this.calculateDamage(baseDamage, defender.defense);
        defender.currentHp = Math.max(0, defender.currentHp - finalDamage);
        
        // Appliquer les effets de statut
        if (jutsu.statusEffect && Math.random() < jutsu.statusChance) {
            this.applyStatusEffect(defender, jutsu.statusEffect);
        }

        let message = `${attacker.name} utilise ${jutsu.name} et inflige ${finalDamage} dégâts!`;
        
        if (elementMultiplier > 1) {
            message += " C'est super efficace!";
        } else if (elementMultiplier < 1) {
            message += " Ce n'est pas très efficace...";
        }

        return {
            success: true,
            damage: finalDamage,
            message: message,
            elementMultiplier: elementMultiplier
        };
    }

    // Calcule les dégâts finaux
    calculateDamage(baseDamage, defense) {
        const damage = Math.max(1, Math.floor(baseDamage - (defense * 0.5)));
        
        // Ajouter une variance de ±10%
        const variance = 0.9 + (Math.random() * 0.2);
        return Math.floor(damage * variance);
    }

    // Applique un effet de statut
    applyStatusEffect(target, effectName) {
        const effect = this.statusEffects[effectName];
        if (!effect) return;

        // Vérifier si l'effet existe déjà
        const existingEffect = target.statusEffects.find(e => e.name === effectName);
        
        if (existingEffect) {
            // Renouveler la durée
            existingEffect.duration = effect.duration;
        } else {
            // Ajouter le nouvel effet
            target.statusEffects.push({
                name: effectName,
                duration: effect.duration,
                ...effect
            });
        }

        this.addToBattleLog(`${target.name} est affecté par ${effectName}!`);
    }

    // Tour de l'ennemi (IA simple)
    executeEnemyTurn() {
        if (this.battleState !== 'enemy_turn' || !this.currentBattle) {
            return;
        }

        const enemy = this.currentBattle.enemy;
        const player = this.currentBattle.player;

        // IA simple : choisir une action
        const action = this.chooseEnemyAction(enemy);
        
        let result = null;

        switch (action.type) {
            case 'attack':
                result = this.executeAttack(enemy, player, action.data);
                break;
            case 'jutsu':
                result = this.executeJutsu(enemy, player, action.data);
                break;
            case 'defend':
                result = this.executeDefend(enemy);
                break;
        }

        if (result) {
            this.addToBattleLog(result.message);
            
            // Mettre à jour les HP du joueur dans gameState
            gameState.player.hp = player.hp;
            
            // Vérifier si le joueur est vaincu
            if (player.hp <= 0) {
                this.endBattle('defeat');
                return;
            }
        }

        // Traiter les effets de statut
        this.processStatusEffects();
        
        // Passer au tour suivant
        this.currentBattle.turn++;
        this.battleState = 'player_turn';
        
        // Déclencher l'événement de nouveau tour
        this.onNewTurn();
    }

    // IA pour choisir l'action de l'ennemi
    chooseEnemyAction(enemy) {
        const actions = [];
        
        // Attaque basique toujours disponible
        actions.push({ type: 'attack', weight: 3, data: { type: 'light' } });
        
        // Jutsu si assez de chakra
        if (enemy.jutsu && enemy.currentChakra >= 15) {
            actions.push({ type: 'jutsu', weight: 2, data: { jutsuId: enemy.jutsu[0] } });
        }
        
        // Défense si HP faibles
        if (enemy.currentHp < enemy.maxHp * 0.3) {
            actions.push({ type: 'defend', weight: 2, data: {} });
        }
        
        // Sélection pondérée
        const totalWeight = actions.reduce((sum, action) => sum + action.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const action of actions) {
            random -= action.weight;
            if (random <= 0) {
                return action;
            }
        }
        
        return actions[0]; // Fallback
    }

    // Traite les effets de statut
    processStatusEffects() {
        [this.currentBattle.player, this.currentBattle.enemy].forEach(character => {
            character.statusEffects = character.statusEffects.filter(effect => {
                // Appliquer l'effet
                if (effect.damagePerTurn) {
                    character.currentHp = Math.max(0, character.currentHp - effect.damagePerTurn);
                    this.addToBattleLog(`${character.name} subit ${effect.damagePerTurn} dégâts de ${effect.name}!`);
                }
                
                // Réduire la durée
                effect.duration--;
                
                // Retourner true si l'effet continue
                return effect.duration > 0;
            });
        });
    }

    // Termine le combat
    endBattle(result) {
        this.battleState = result;
        
        if (result === 'victory') {
            this.onVictory();
        } else if (result === 'defeat') {
            this.onDefeat();
        }
        
        // Nettoyer les données de combat
        setTimeout(() => {
            this.currentBattle = null;
            this.battleState = 'idle';
        }, 3000);
    }

    // Gestion de la victoire
    onVictory() {
        const enemy = this.currentBattle.enemy;
        const xpGained = enemy.xp || 0;
        const ryoGained = enemy.ryo || Math.floor(enemy.level * 10);
        
        // Donner XP et argent
        gameState.gainXP(xpGained);
        gameState.inventory.ryo += ryoGained;
        
        // Loot
        if (enemy.loot) {
            this.processLoot(enemy.loot);
        }
        
        // Statistiques
        gameState.progress.enemiesDefeated++;
        
        this.addToBattleLog(`Victoire! +${xpGained} XP, +${ryoGained} Ryo`);
        
        console.log("Combat gagné!");
    }

    // Gestion de la défaite
    onDefeat() {
        // Téléporter au dernier checkpoint
        const lastCheckpoint = this.getLastCheckpoint();
        gameState.updatePlayerPosition(lastCheckpoint.x, lastCheckpoint.y);
        
        // Restaurer HP/Chakra
        gameState.player.hp = gameState.player.maxHp;
        gameState.player.chakra = gameState.player.maxChakra;
        
        this.addToBattleLog("Défaite... Vous vous réveillez au dernier checkpoint.");
        
        console.log("Combat perdu!");
    }

    // Obtient les données d'un jutsu
    getJutsuData(jutsuId) {
        // Cette fonction sera liée à la base de données des jutsu
        const jutsuDatabase = {
            'dragon_slash': {
                name: "Tranche du Dragon",
                type: 'damage',
                element: 'fire',
                chakraCost: 20,
                baseDamage: 25,
                attackMultiplier: 0.8,
                statusEffect: 'burn',
                statusChance: 0.3
            },
            'shadow_clone': {
                name: "Clone d'Ombre",
                type: 'buff',
                element: 'shadow',
                chakraCost: 30,
                effect: 'boost_attack',
                duration: 3
            },
            'wind_cutter': {
                name: "Lame de Vent",
                type: 'damage',
                element: 'wind',
                chakraCost: 15,
                baseDamage: 20,
                attackMultiplier: 1.0
            }
        };
        
        return jutsuDatabase[jutsuId];
    }

    // Ajoute un message au log de combat
    addToBattleLog(message) {
        this.battleLog.push({
            message: message,
            timestamp: Date.now()
        });
        
        // Garder seulement les 20 derniers messages
        if (this.battleLog.length > 20) {
            this.battleLog.shift();
        }
        
        console.log(`[COMBAT] ${message}`);
    }

    // Événements de combat
    onBattleStart() {
        console.log("Combat commencé!");
        // Ici on pourrait déclencher des animations, changer la musique, etc.
    }

    onNewTurn() {
        console.log(`Tour ${this.currentBattle.turn}`);
        // Mettre à jour l'UI, traiter les effets, etc.
    }

    // Obtient le dernier checkpoint
    getLastCheckpoint() {
        // Retourner la position de départ par défaut
        return { x: 25, y: 25 }; // Position de départ par défaut
    }

    // Obtient l'élément d'un clan
    getClanElement(clan) {
        const clanElements = {
            ryujin: 'fire',
            yurei: 'shadow',
            kaze: 'wind'
        };
        return clanElements[clan] || 'neutral';
    }

    // Calcule le multiplicateur élémentaire
    getElementMultiplier(attackElement, defenseElement) {
        const weaknesses = {
            fire: ['wind', 'nature'],
            water: ['fire'],
            earth: ['water'],
            wind: ['earth'],
            lightning: ['water'],
            shadow: ['light'],
            light: ['shadow']
        };

        const resistances = {
            fire: ['water', 'earth'],
            water: ['earth', 'lightning'],
            earth: ['wind', 'fire'],
            wind: ['lightning'],
            lightning: ['earth'],
            shadow: ['fire'],
            light: ['shadow']
        };

        if (weaknesses[attackElement]?.includes(defenseElement)) {
            return 1.5; // Super efficace
        } else if (resistances[attackElement]?.includes(defenseElement)) {
            return 0.75; // Peu efficace
        } else {
            return 1.0; // Efficacité normale
        }
    }

    // Nettoyage
    cleanup() {
        this.currentBattle = null;
        this.battleState = 'idle';
    }
}

// Instance globale du système de combat
const combatSystem = new CombatSystem();
