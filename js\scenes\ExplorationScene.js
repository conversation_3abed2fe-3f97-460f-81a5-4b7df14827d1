/**
 * Scène d'exploration principale pour Shinobi Chronicles
 * Gère le mouvement du joueur, la carte, et les événements du monde
 */

class ExplorationScene extends Phaser.Scene {
    constructor() {
        super({ key: 'ExplorationScene' });
        
        // Propriétés de la scène
        this.player = null;
        this.cursors = null;
        this.fogOfWar = null;
        this.worldMap = null;
        this.currentRegion = 'konoha_forest';
        
        // Système de tuiles
        this.tileSize = 32; // Taille des tuiles
        this.mapWidth = 50; // Largeur de la carte
        this.mapHeight = 50; // Hauteur de la carte
        
        // Gestion des événements
        this.lastMoveTime = 0;
        this.moveDelay = 200; // <PERSON><PERSON>lai entre les mouvements en ms
        this.isMoving = false;
        
        // Cache des tuiles
        this.tileCache = new Map();
        this.eventTiles = new Map();
    }

    create() {
        console.log('🗺️ Initialisation de la scène d\'exploration');
        
        // Initialiser les systèmes
        this.initializeWorld();
        this.createPlayer();
        this.setupCamera();
        this.setupControls();
        // this.initializeFogOfWar(); // Temporairement désactivé pour debug
        this.generateWorldEvents();
        
        // Interface
        this.createUI();
        
        // Démarrer les systèmes
        this.startSystems();
        
        // Test de visibilité
        this.createVisibilityTest();

        console.log('✅ Scène d\'exploration prête');
    }

    createVisibilityTest() {
        // Créer des éléments de test visibles pour s'assurer que Phaser fonctionne
        const { width, height } = this.cameras.main;

        // Rectangle de test au centre
        const testRect = this.add.rectangle(width/2, height/2, 100, 100, 0xff0000);
        testRect.setDepth(1000);

        // Texte de test
        const testText = this.add.text(width/2, height/2 - 150, 'TEST VISIBILITÉ', {
            fontSize: '24px',
            color: '#ffffff',
            backgroundColor: '#000000'
        }).setOrigin(0.5);
        testText.setDepth(1000);

        console.log('🔍 Test de visibilité créé');
    }

    initializeWorld() {
        const { width, height } = this.cameras.main;

        // Créer un fond visible pour la scène
        this.add.rectangle(0, 0, this.mapWidth * this.tileSize, this.mapHeight * this.tileSize, 0x2c3e50).setOrigin(0, 0);

        // Créer le fond du monde
        this.worldContainer = this.add.container(0, 0);

        // Générer la carte procédurale
        this.generateMap();

        // Définir les limites du monde
        this.physics.world.setBounds(0, 0, this.mapWidth * this.tileSize, this.mapHeight * this.tileSize);

        console.log(`🌍 Monde initialisé: ${this.mapWidth * this.tileSize}x${this.mapHeight * this.tileSize} pixels`);
    }

    generateMap() {
        console.log('🌍 Génération de la carte...');
        
        // Créer une grille de tuiles
        this.mapGrid = [];
        
        for (let y = 0; y < this.mapHeight; y++) {
            this.mapGrid[y] = [];
            for (let x = 0; x < this.mapWidth; x++) {
                const tileType = this.determineTileType(x, y);
                this.mapGrid[y][x] = tileType;
                
                // Créer la tuile visuelle
                this.createTile(x, y, tileType);
            }
        }
        
        console.log(`✅ Carte générée: ${this.mapWidth}x${this.mapHeight} tuiles`);
    }

    determineTileType(x, y) {
        // Génération procédurale simple basée sur la position
        const centerX = this.mapWidth / 2;
        const centerY = this.mapHeight / 2;
        const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        
        // Zone de départ (village)
        if (distanceFromCenter < 3) {
            return 'village';
        }
        
        // Forêt dense
        if (distanceFromCenter < 15) {
            return Math.random() < 0.7 ? 'forest' : 'grass';
        }
        
        // Zones plus variées selon la région
        const noise = Math.sin(x * 0.1) * Math.cos(y * 0.1);
        
        if (noise > 0.3) {
            return 'forest';
        } else if (noise > 0) {
            return 'grass';
        } else if (noise > -0.3) {
            return 'water';
        } else {
            return 'mountain';
        }
    }

    createTile(x, y, tileType) {
        const worldX = x * this.tileSize;
        const worldY = y * this.tileSize;

        // Choisir la texture selon le type
        let texture = 'grass_tile';
        let tint = 0xffffff;

        switch (tileType) {
            case 'village':
                texture = 'grass_tile';
                tint = 0xffff00; // Jaune vif pour le village
                break;
            case 'forest':
                texture = 'tree_tile';
                tint = 0x00ff00; // Vert vif pour la forêt
                break;
            case 'grass':
                texture = 'grass_tile';
                tint = 0x90EE90; // Vert clair pour l'herbe
                break;
            case 'water':
                texture = 'water_tile';
                tint = 0x0080ff; // Bleu vif pour l'eau
                break;
            case 'mountain':
                texture = 'tree_tile';
                tint = 0x808080; // Gris pour les montagnes
                break;
        }

        // Créer le sprite de tuile
        const tile = this.add.image(worldX + this.tileSize/2, worldY + this.tileSize/2, texture);
        tile.setDisplaySize(this.tileSize, this.tileSize);
        tile.setTint(tint);
        tile.setDepth(0);

        // Ajouter au container du monde
        this.worldContainer.add(tile);

        // Debug: log pour les premières tuiles
        if (x < 3 && y < 3) {
            console.log(`🟩 Tuile créée: (${x},${y}) type=${tileType} texture=${texture} tint=${tint.toString(16)}`);
        }

        // Stocker dans le cache
        this.tileCache.set(`${x},${y}`, {
            type: tileType,
            sprite: tile,
            x: x,
            y: y
        });
    }

    createPlayer() {
        // Position de départ du joueur
        const startPos = gameState.player.position;
        const worldX = startPos.x * this.tileSize + this.tileSize/2;
        const worldY = startPos.y * this.tileSize + this.tileSize/2;
        
        // Choisir le sprite selon le clan
        let playerSprite = 'player_sprite'; // fallback
        switch (gameState.player.clan) {
            case 'ryujin':
                playerSprite = 'samurai_sprite'; // Clan du feu = samurai
                break;
            case 'yurei':
                playerSprite = 'shinobi_sprite'; // Clan de l'ombre = shinobi
                break;
            case 'kaze':
                playerSprite = 'fighter_sprite'; // Clan du vent = fighter
                break;
        }

        // Créer le sprite du joueur
        this.player = this.physics.add.sprite(worldX, worldY, playerSprite);
        this.player.setDisplaySize(this.tileSize * 0.8, this.tileSize * 0.8);
        this.player.setDepth(100);
        
        // Appliquer la couleur du clan
        const clanColors = {
            ryujin: 0xe74c3c,
            yurei: 0x9b59b6,
            kaze: 0x3498db
        };

        const clanColor = clanColors[gameState.player.clan];
        if (clanColor) {
            this.player.setTint(clanColor);
        }
        
        // Configuration physique
        this.player.setCollideWorldBounds(true);
        this.player.body.setSize(this.tileSize * 0.6, this.tileSize * 0.6);
        
        console.log(`👤 Joueur créé à la position (${startPos.x}, ${startPos.y})`);
    }

    setupCamera() {
        // Configurer la caméra pour suivre le joueur
        this.cameras.main.startFollow(this.player);
        this.cameras.main.setLerp(0.1, 0.1);
        this.cameras.main.setZoom(1); // Zoom normal pour voir la carte

        // Limites de la caméra
        this.cameras.main.setBounds(0, 0, this.mapWidth * this.tileSize, this.mapHeight * this.tileSize);

        // Debug: afficher les informations de la caméra
        console.log(`📷 Caméra configurée: zoom=${this.cameras.main.zoom}, bounds=${this.mapWidth * this.tileSize}x${this.mapHeight * this.tileSize}`);
    }

    setupControls() {
        // Contrôles clavier
        this.cursors = this.input.keyboard.createCursorKeys();
        
        // Touches WASD
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');
        
        // Touches d'action
        this.actionKeys = this.input.keyboard.addKeys('SPACE,F,I,M,ESC');
        
        // Écouter les événements de touches
        this.input.keyboard.on('keydown', this.handleKeyDown, this);
    }

    initializeFogOfWar() {
        // Créer le système de fog of war
        this.fogOfWar = new FogOfWar(this);
        
        // Révéler la zone autour du joueur
        const playerTileX = Math.floor(this.player.x / this.tileSize);
        const playerTileY = Math.floor(this.player.y / this.tileSize);
        this.fogOfWar.updateFogAroundPlayer(this.player.x, this.player.y);
    }

    generateWorldEvents() {
        console.log('🎲 Génération des événements du monde...');
        
        // Générer des événements aléatoires sur la carte
        const eventCount = Math.floor(this.mapWidth * this.mapHeight * 0.05); // 5% des tuiles
        
        for (let i = 0; i < eventCount; i++) {
            const x = Phaser.Math.Between(5, this.mapWidth - 5);
            const y = Phaser.Math.Between(5, this.mapHeight - 5);
            const eventType = this.getRandomEventType();
            
            this.eventTiles.set(`${x},${y}`, {
                type: eventType,
                triggered: false,
                data: this.generateEventData(eventType)
            });
        }
        
        console.log(`✅ ${eventCount} événements générés`);
    }

    getRandomEventType() {
        const eventTypes = ['enemy', 'treasure', 'npc', 'shrine', 'trap'];
        const weights = [0.4, 0.2, 0.15, 0.1, 0.15]; // Probabilités
        
        const random = Math.random();
        let cumulative = 0;
        
        for (let i = 0; i < eventTypes.length; i++) {
            cumulative += weights[i];
            if (random <= cumulative) {
                return eventTypes[i];
            }
        }
        
        return 'enemy'; // Fallback
    }

    generateEventData(eventType) {
        switch (eventType) {
            case 'enemy':
                return this.generateEnemyData();
            case 'treasure':
                return this.generateTreasureData();
            case 'npc':
                return this.generateNPCData();
            case 'shrine':
                return { type: 'chakra_shrine', bonus: 50 };
            case 'trap':
                return { type: 'spike_trap', damage: 10 };
            default:
                return {};
        }
    }

    generateEnemyData() {
        // Choisir un ennemi approprié au niveau du joueur
        const playerLevel = gameState.player.level;
        const enemies = window.gameData?.enemies || [];
        
        const suitableEnemies = enemies.filter(enemy => 
            enemy.level >= playerLevel - 1 && enemy.level <= playerLevel + 2
        );
        
        if (suitableEnemies.length > 0) {
            const randomEnemy = Phaser.Utils.Array.GetRandom(suitableEnemies);
            return { enemyId: randomEnemy.id };
        }
        
        return { enemyId: 'wild_boar' }; // Fallback
    }

    generateTreasureData() {
        const treasures = [
            { type: 'ryo', amount: Phaser.Math.Between(10, 50) },
            { type: 'item', id: 'chakra_pill', quantity: Phaser.Math.Between(1, 3) },
            { type: 'item', id: 'healing_salve', quantity: Phaser.Math.Between(1, 2) }
        ];
        
        return Phaser.Utils.Array.GetRandom(treasures);
    }

    generateNPCData() {
        const npcs = [
            { type: 'merchant', name: 'Marchand Itinérant' },
            { type: 'trainer', name: 'Maître Ninja' },
            { type: 'villager', name: 'Villageois' }
        ];
        
        return Phaser.Utils.Array.GetRandom(npcs);
    }

    update(time, delta) {
        // Mettre à jour le mouvement du joueur
        this.updatePlayerMovement(time);
        
        // Mettre à jour le fog of war (temporairement désactivé)
        // if (this.fogOfWar) {
        //     this.fogOfWar.updateFogAroundPlayer(this.player.x, this.player.y);
        // }
        
        // Vérifier les événements de tuile
        this.checkTileEvents();
        
        // Mettre à jour l'interface
        this.updateUI();
    }

    updatePlayerMovement(time) {
        if (this.isMoving || time - this.lastMoveTime < this.moveDelay) {
            return;
        }
        
        let moveX = 0;
        let moveY = 0;
        
        // Vérifier les entrées
        if (this.cursors.left.isDown || this.wasd.A.isDown) {
            moveX = -1;
        } else if (this.cursors.right.isDown || this.wasd.D.isDown) {
            moveX = 1;
        }
        
        if (this.cursors.up.isDown || this.wasd.W.isDown) {
            moveY = -1;
        } else if (this.cursors.down.isDown || this.wasd.S.isDown) {
            moveY = 1;
        }
        
        // Effectuer le mouvement
        if (moveX !== 0 || moveY !== 0) {
            this.movePlayer(moveX, moveY);
            this.lastMoveTime = time;
        }
    }

    movePlayer(deltaX, deltaY) {
        const currentTileX = Math.floor(this.player.x / this.tileSize);
        const currentTileY = Math.floor(this.player.y / this.tileSize);
        
        const newTileX = currentTileX + deltaX;
        const newTileY = currentTileY + deltaY;
        
        // Vérifier les limites
        if (newTileX < 0 || newTileX >= this.mapWidth || newTileY < 0 || newTileY >= this.mapHeight) {
            return;
        }
        
        // Vérifier le coût en chakra
        const movementCost = this.getMovementCost(newTileX, newTileY);
        if (!chakraSystem.spendChakra(movementCost, 'movement')) {
            return;
        }
        
        // Effectuer le mouvement
        this.isMoving = true;
        
        const newWorldX = newTileX * this.tileSize + this.tileSize/2;
        const newWorldY = newTileY * this.tileSize + this.tileSize/2;
        
        // Animation de mouvement
        this.tweens.add({
            targets: this.player,
            x: newWorldX,
            y: newWorldY,
            duration: this.moveDelay * 0.8,
            ease: 'Power2',
            onComplete: () => {
                this.isMoving = false;
                this.onPlayerMoved(newTileX, newTileY);
            }
        });
        
        // Mettre à jour la position dans le gameState
        gameState.updatePlayerPosition(newTileX, newTileY);
    }

    getMovementCost(tileX, tileY) {
        const tile = this.tileCache.get(`${tileX},${tileY}`);
        
        if (tile) {
            switch (tile.type) {
                case 'water':
                case 'mountain':
                    return 5; // Coût élevé pour terrain difficile
                default:
                    return 1; // Coût normal
            }
        }

        return 1; // Coût par défaut
    }

    onPlayerMoved(tileX, tileY) {
        console.log(`👤 Joueur déplacé vers (${tileX}, ${tileY})`);
        
        // Vérifier les événements sur cette tuile
        this.checkTileEvents();
    }

    checkTileEvents() {
        const playerTileX = Math.floor(this.player.x / this.tileSize);
        const playerTileY = Math.floor(this.player.y / this.tileSize);
        const tileKey = `${playerTileX},${playerTileY}`;
        
        const event = this.eventTiles.get(tileKey);
        if (event && !event.triggered) {
            this.triggerEvent(event, playerTileX, playerTileY);
            event.triggered = true;
        }
    }

    triggerEvent(event, tileX, tileY) {
        console.log(`🎯 Événement déclenché: ${event.type} à (${tileX}, ${tileY})`);
        
        switch (event.type) {
            case 'enemy':
                this.startCombat(event.data);
                break;
            case 'treasure':
                this.openTreasure(event.data);
                break;
            case 'npc':
                this.talkToNPC(event.data);
                break;
            case 'shrine':
                this.useShrine(event.data);
                break;
            case 'trap':
                this.triggerTrap(event.data);
                break;
        }
    }

    startCombat(enemyData) {
        console.log(`⚔️ Combat contre ${enemyData.enemyId}`);
        
        // Trouver les données de l'ennemi
        const enemies = window.gameData?.enemies || [];
        const enemy = enemies.find(e => e.id === enemyData.enemyId);
        
        if (enemy) {
            // Démarrer la scène de combat
            this.scene.launch('BattleScene', { enemy: enemy });
            this.scene.pause();
        }
    }

    openTreasure(treasureData) {
        console.log(`💰 Trésor trouvé:`, treasureData);
        
        if (treasureData.type === 'ryo') {
            gameState.inventory.ryo += treasureData.amount;
            this.showFloatingText(`+${treasureData.amount} Ryo`, 0xffd700);
        } else if (treasureData.type === 'item') {
            gameState.addItem(treasureData.id, treasureData.quantity);
            this.showFloatingText(`+${treasureData.quantity} ${treasureData.id}`, 0x00ff00);
        }
    }

    showFloatingText(text, color = 0xffffff) {
        const floatingText = this.add.text(this.player.x, this.player.y - 30, text, {
            fontSize: '16px',
            color: `#${color.toString(16).padStart(6, '0')}`,
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);
        
        this.tweens.add({
            targets: floatingText,
            y: floatingText.y - 50,
            alpha: 0,
            duration: 2000,
            ease: 'Power2',
            onComplete: () => {
                floatingText.destroy();
            }
        });
    }

    createUI() {
        // L'interface est gérée par le HTML/CSS
        // Ici on peut ajouter des éléments UI spécifiques à Phaser si nécessaire
    }

    updateUI() {
        // Mettre à jour l'interface utilisateur
        if (window.shinobiChronicles) {
            window.shinobiChronicles.updateUI();
        }
    }

    startSystems() {
        // Démarrer les systèmes automatiques
        console.log('🔧 Démarrage des systèmes...');
    }

    handleKeyDown(event) {
        switch (event.code) {
            case 'Space':
                this.handleInteraction();
                break;
            case 'KeyF':
                this.handleScan();
                break;
        }
    }

    handleInteraction() {
        console.log('🤝 Interaction');
        // TODO: Implémenter les interactions
    }

    handleScan() {
        console.log('👁️ Scan de la zone');
        
        if (chakraSystem.spendChakra(5, 'scan')) {
            // Révéler une zone plus large temporairement
            const playerTileX = Math.floor(this.player.x / this.tileSize);
            const playerTileY = Math.floor(this.player.y / this.tileSize);
            
            this.fogOfWar.revealArea(playerTileX, playerTileY, 5);
            this.showFloatingText('Zone scannée!', 0x00ffff);
        }
    }

    // Méthode appelée depuis l'extérieur pour gérer le mouvement mobile
    handleMovement(direction) {
        const directionMap = {
            'up': { x: 0, y: -1 },
            'down': { x: 0, y: 1 },
            'left': { x: -1, y: 0 },
            'right': { x: 1, y: 0 }
        };
        
        const delta = directionMap[direction];
        if (delta) {
            this.movePlayer(delta.x, delta.y);
        }
    }
}
