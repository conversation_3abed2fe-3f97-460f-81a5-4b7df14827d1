/**
 * Scène de chargement pour Shinobi Chronicles
 * Charge tous les assets nécessaires au jeu
 */

class LoadingScene extends Phaser.Scene {
    constructor() {
        super({ key: 'LoadingScene' });
        this.loadingProgress = 0;
    }

    preload() {
        console.log('🔄 Chargement des assets...');

        // Créer la barre de progression
        this.createLoadingBar();

        // Écouter les événements de chargement
        this.load.on('progress', this.updateProgress, this);
        this.load.on('complete', this.onLoadComplete, this);
        this.load.on('fileprogress', this.onFileProgress, this);

        // Charger les assets
        this.loadAssets();
    }

    createLoadingBar() {
        const { width, height } = this.cameras.main;
        
        // Fond
        this.add.rectangle(width / 2, height / 2, width, height, 0x1a1a2e);
        
        // Titre
        const title = this.add.text(width / 2, height / 2 - 100, 'Shinobi Chronicles', {
            fontSize: '48px',
            fontFamily: 'Arial',
            color: '#ff6b6b',
            stroke: '#000000',
            strokeThickness: 4
        }).setOrigin(0.5);
        
        // Barre de progression - fond
        this.progressBarBg = this.add.rectangle(width / 2, height / 2, 400, 20, 0x333333);
        this.progressBarBg.setStrokeStyle(2, 0x666666);
        
        // Barre de progression - remplissage
        this.progressBar = this.add.rectangle(width / 2 - 200, height / 2, 0, 16, 0x00ff00);
        this.progressBar.setOrigin(0, 0.5);
        
        // Texte de progression
        this.progressText = this.add.text(width / 2, height / 2 + 40, '0%', {
            fontSize: '24px',
            fontFamily: 'Arial',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        // Texte de fichier en cours
        this.fileText = this.add.text(width / 2, height / 2 + 80, 'Initialisation...', {
            fontSize: '16px',
            fontFamily: 'Arial',
            color: '#cccccc'
        }).setOrigin(0.5);
    }

    loadAssets() {
        // Générer des textures procédurales
        this.generatePlaceholderTextures();

        // Charger les données JSON
        this.loadGameData();

        // Charger les vrais assets
        this.loadRealAssets();
    }

    loadPlaceholderAssets() {
        // Générer des textures procédurales pour les tests
        this.generatePlaceholderTextures();
    }

    generatePlaceholderTextures() {
        // Texture du joueur (carré bleu)
        this.textures.generate('player_sprite', {
            data: ['2222', '2112', '2112', '2222'],
            pixelWidth: 8,
            pixelHeight: 8
        });
        
        // Texture d'ennemi (carré rouge)
        this.textures.generate('enemy_sprite', {
            data: ['3333', '3113', '3113', '3333'],
            pixelWidth: 8,
            pixelHeight: 8
        });
        
        // Tilesets de base
        this.textures.generate('grass_tile', {
            data: ['4444', '4554', '4554', '4444'],
            pixelWidth: 8,
            pixelHeight: 8
        });
        
        this.textures.generate('tree_tile', {
            data: ['6666', '6776', '6776', '6666'],
            pixelWidth: 8,
            pixelHeight: 8
        });
        
        this.textures.generate('water_tile', {
            data: ['8888', '8998', '8998', '8888'],
            pixelWidth: 8,
            pixelHeight: 8
        });
        
        // Texture de fog
        this.textures.generate('fog_tile', {
            data: ['0000', '0000', '0000', '0000'],
            pixelWidth: 8,
            pixelHeight: 8
        });
    }

    loadGameData() {
        // Charger les données JSON
        this.load.json('enemies_data', 'data/enemies.json');
        this.load.json('items_data', 'data/items.json');
        this.load.json('jutsu_data', 'data/jutsu.json');

        // Les cartes seront générées procéduralement
        console.log('📊 Données JSON en cours de chargement...');
    }

    loadRealAssets() {
        // Charger les atlas de sprites ninja avec animations
        this.load.atlas('shinobi_atlas', 'assets/images/sprites/shinobi.png', 'assets/images/sprites/shinobi.json');
        this.load.atlas('fighter_atlas', 'assets/images/sprites/fighter.png', 'assets/images/sprites/fighter.json');
        this.load.atlas('samurai_atlas', 'assets/images/sprites/samurai.png', 'assets/images/sprites/samurai.json');

        // Charger aussi les images simples comme fallback
        this.load.image('shinobi_sprite', 'assets/images/sprites/shinobi.png');
        this.load.image('fighter_sprite', 'assets/images/sprites/fighter.png');
        this.load.image('samurai_sprite', 'assets/images/sprites/samurai.png');

        // Gestion d'erreur pour les fichiers manquants
        this.load.on('loaderror', (file) => {
            console.warn(`Fichier manquant: ${file.src}, utilisation du fallback`);
            // Créer une texture de fallback
            this.textures.generate(file.key, {
                data: ['3333', '3113', '3113', '3333'],
                pixelWidth: 8,
                pixelHeight: 8
            });
        });

        // Charger l'image du village si disponible
        this.load.image('village_map', 'assets/images/village.png');

        // Quelques placeholders pour la barre de progression
        for (let i = 0; i < 3; i++) {
            this.load.image(`placeholder_${i}`, 'assets/images/placeholder.png');
        }
    }

    loadVisualAssets() {
        // Pour l'instant, on utilise des textures générées procéduralement
        // Plus tard, on chargera les vrais sprites depuis des fichiers
        console.log('🎨 Assets visuels générés procéduralement');
    }

    loadAudioAssets() {
        // Placeholders audio (silence)
        // Plus tard, on chargera les vrais fichiers audio
        
        // Musiques
        // this.load.audio('village_theme', 'assets/audio/music/village_theme.ogg');
        // this.load.audio('forest_theme', 'assets/audio/music/forest_theme.ogg');
        
        // Effets sonores
        // this.load.audio('attack_sound', 'assets/audio/sfx/attack.wav');
        // this.load.audio('jutsu_sound', 'assets/audio/sfx/jutsu.wav');
    }

    updateProgress(progress) {
        this.loadingProgress = progress;
        
        // Mettre à jour la barre visuelle
        this.progressBar.width = 400 * progress;
        
        // Changer la couleur selon le progrès
        if (progress < 0.3) {
            this.progressBar.setFillStyle(0xff6b6b); // Rouge
        } else if (progress < 0.7) {
            this.progressBar.setFillStyle(0xfeca57); // Orange
        } else {
            this.progressBar.setFillStyle(0x48dbfb); // Bleu
        }
        
        // Mettre à jour le texte
        this.progressText.setText(Math.round(progress * 100) + '%');
    }

    onFileProgress(file) {
        // Afficher le fichier en cours de chargement
        this.fileText.setText(`Chargement: ${file.key}`);
    }

    onLoadComplete() {
        console.log('✅ Chargement terminé');

        // Effet de fin de chargement
        this.progressText.setText('100% - Terminé!');
        this.fileText.setText('Prêt à jouer!');

        // Stocker les données chargées globalement
        this.storeGameData();

        // Créer les animations des sprites
        this.createSpriteAnimations();

        // Transition vers la scène d'exploration après un délai
        this.time.delayedCall(1000, () => {
            this.scene.start('ExplorationScene');
        });
    }

    createSpriteAnimations() {
        console.log('🎬 Création des animations...');

        // Fonction pour inspecter un atlas et lister ses frames
        const inspectAtlas = (atlasKey) => {
            if (!this.textures.exists(atlasKey)) {
                return [];
            }

            const texture = this.textures.get(atlasKey);
            const frameNames = Object.keys(texture.frames);
            console.log(`🔍 Atlas ${atlasKey} contient:`, frameNames);
            return frameNames;
        };

        // Fonction pour créer une animation simple avec une seule frame
        const createStaticAnimation = (key, atlasKey, frameName) => {
            try {
                this.anims.create({
                    key: key,
                    frames: [{ key: atlasKey, frame: frameName }],
                    frameRate: 1,
                    repeat: -1
                });
                console.log(`✅ Animation statique ${key} créée avec frame ${frameName}`);
                return true;
            } catch (error) {
                console.log(`❌ Erreur création animation ${key}:`, error);
                return false;
            }
        };

        // Fonction pour créer des animations intelligentes
        const createSmartAnimation = (animKey, atlasKey, actionType) => {
            const frameNames = inspectAtlas(atlasKey);
            if (frameNames.length === 0) {
                console.log(`⚠️ Atlas ${atlasKey} vide ou inexistant`);
                return false;
            }

            // Chercher des frames qui correspondent à l'action
            const matchingFrames = frameNames.filter(name =>
                name.toLowerCase().includes(actionType.toLowerCase()) ||
                name.toLowerCase().includes(actionType.toLowerCase().substring(0, 4))
            );

            if (matchingFrames.length > 0) {
                // Utiliser les frames trouvées
                try {
                    const frames = matchingFrames.map(frameName => ({ key: atlasKey, frame: frameName }));
                    this.anims.create({
                        key: animKey,
                        frames: frames,
                        frameRate: actionType === 'idle' ? 6 : 10,
                        repeat: actionType === 'attack' ? 0 : -1
                    });
                    console.log(`✅ Animation ${animKey} créée avec ${matchingFrames.length} frames`);
                    return true;
                } catch (error) {
                    console.log(`❌ Erreur création animation ${animKey}:`, error);
                }
            }

            // Fallback: utiliser la première frame disponible
            if (frameNames.length > 0) {
                return createStaticAnimation(animKey, atlasKey, frameNames[0]);
            }

            return false;
        };

        // Créer les animations pour chaque sprite
        const sprites = [
            { name: 'shinobi', atlas: 'shinobi_atlas' },
            { name: 'fighter', atlas: 'fighter_atlas' },
            { name: 'samurai', atlas: 'samurai_atlas' }
        ];

        const actions = ['idle', 'walk', 'attack'];

        sprites.forEach(sprite => {
            actions.forEach(action => {
                const animKey = `${sprite.name}_${action}`;
                createSmartAnimation(animKey, sprite.atlas, action);
            });
        });

        console.log('🎬 Animations créées avec système intelligent');
    }

    storeGameData() {
        // Stocker les données JSON dans des variables globales pour un accès facile
        if (this.cache.json.exists('enemies_data')) {
            window.gameData = window.gameData || {};
            window.gameData.enemies = this.cache.json.get('enemies_data');
            console.log('📊 Données des ennemis chargées:', window.gameData.enemies.length, 'ennemis');
        }
        
        if (this.cache.json.exists('items_data')) {
            window.gameData = window.gameData || {};
            window.gameData.items = this.cache.json.get('items_data');
            console.log('📊 Données des objets chargées:', window.gameData.items.length, 'objets');
        }
        
        if (this.cache.json.exists('jutsu_data')) {
            window.gameData = window.gameData || {};
            window.gameData.jutsu = this.cache.json.get('jutsu_data');
            console.log('📊 Données des jutsu chargées:', window.gameData.jutsu.length, 'jutsu');
        }
    }

    create() {
        // Cette méthode est appelée après preload()
        // Ici on peut ajouter des éléments visuels supplémentaires si nécessaire
        
        // Ajouter des particules de fond pour l'effet
        this.createBackgroundEffects();
    }

    createBackgroundEffects() {
        const { width, height } = this.cameras.main;
        
        // Créer quelques particules flottantes
        for (let i = 0; i < 20; i++) {
            const particle = this.add.circle(
                Phaser.Math.Between(0, width),
                Phaser.Math.Between(0, height),
                Phaser.Math.Between(1, 3),
                0x48dbfb,
                0.3
            );
            
            // Animation flottante
            this.tweens.add({
                targets: particle,
                y: particle.y - 50,
                alpha: 0,
                duration: Phaser.Math.Between(3000, 6000),
                ease: 'Power2',
                repeat: -1,
                yoyo: true
            });
        }
    }

    // Méthode utilitaire pour créer des assets de test
    static createTestAssets(scene) {
        // Cette méthode peut être appelée par d'autres scènes pour créer des assets de test
        
        // Créer une texture de test pour les ennemis
        scene.textures.generate('test_enemy', {
            data: [
                '..cc..',
                '.c11c.',
                'c1111c',
                'c1111c',
                '.c11c.',
                '..cc..'
            ],
            pixelWidth: 4,
            pixelHeight: 4
        });
        
        // Créer une texture de test pour les objets
        scene.textures.generate('test_item', {
            data: [
                '..22..',
                '.2332.',
                '23332',
                '23332',
                '.222.',
                '......'
            ],
            pixelWidth: 4,
            pixelHeight: 4
        });
    }
}

// Fonction utilitaire pour vérifier si tous les assets sont chargés
LoadingScene.checkAssetsLoaded = function() {
    return window.gameData && 
           window.gameData.enemies && 
           window.gameData.items && 
           window.gameData.jutsu;
};

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoadingScene;
}
