<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shinobi Chronicles - Shadow of the Ninja</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/combat.css">
</head>
<body>
    <div id="game-container">
        <!-- Loading Screen -->
        <div id="loading-screen" class="screen active">
            <div class="loading-content">
                <h1>Shinobi Chronicles</h1>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p>Chargement du monde ninja...</p>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="main-menu" class="screen">
            <div class="menu-content">
                <h1>Shinobi Chronicles</h1>
                <div class="menu-buttons">
                    <button id="new-game-btn" class="menu-btn">Nouvelle Partie</button>
                    <button id="load-game-btn" class="menu-btn">Continuer</button>
                    <button id="settings-btn" class="menu-btn">Paramètres</button>
                    <button id="debug-btn" class="menu-btn debug-btn" style="background: #e74c3c; font-size: 12px;">🔧 Debug</button>
                </div>
            </div>
        </div>

        <!-- Character Creation -->
        <div id="character-creation" class="screen">
            <div class="creation-content">
                <h2>Créer votre Ninja</h2>
                <div class="clan-selection">
                    <h3>Choisissez votre Clan</h3>
                    <div class="clan-options">
                        <div class="clan-option" data-clan="ryujin">
                            <h4>Clan Ryūjin</h4>
                            <p>Maîtres du jutsu dragon et des techniques de feu</p>
                        </div>
                        <div class="clan-option" data-clan="yurei">
                            <h4>Clan Yūrei</h4>
                            <p>Spécialistes de la furtivité et des illusions</p>
                        </div>
                        <div class="clan-option" data-clan="kaze">
                            <h4>Clan Kaze</h4>
                            <p>Maîtres du vent et de la vitesse</p>
                        </div>
                    </div>
                </div>
                <button id="start-game-btn" class="menu-btn" disabled>Commencer l'Aventure</button>
            </div>
        </div>

        <!-- Game Canvas -->
        <div id="game-canvas" class="screen">
            <!-- Phaser game will be injected here -->
        </div>

        <!-- Game UI Overlay -->
        <div id="game-ui" class="ui-overlay">
            <!-- HUD -->
            <div id="hud" class="hud">
                <div class="stats-panel">
                    <div class="stat-bar">
                        <label>HP</label>
                        <div class="bar hp-bar">
                            <div class="bar-fill" id="hp-fill"></div>
                            <span class="bar-text" id="hp-text">100/100</span>
                        </div>
                    </div>
                    <div class="stat-bar">
                        <label>Chakra</label>
                        <div class="bar chakra-bar">
                            <div class="bar-fill" id="chakra-fill"></div>
                            <span class="bar-text" id="chakra-text">20/20</span>
                        </div>
                    </div>
                    <div class="level-info">
                        <span id="level-text">Niveau 1</span>
                        <div class="xp-bar">
                            <div class="bar-fill" id="xp-fill"></div>
                            <span class="bar-text" id="xp-text">0/100</span>
                        </div>
                    </div>
                </div>

                <div class="mini-map">
                    <canvas id="mini-map-canvas" width="150" height="150"></canvas>
                </div>

                <div class="quick-actions">
                    <button class="quick-btn" id="inventory-btn">📦</button>
                    <button class="quick-btn" id="skills-btn">⚡</button>
                    <button class="quick-btn" id="map-btn">🗺️</button>
                    <button class="quick-btn" id="menu-btn">⚙️</button>
                </div>
            </div>

            <!-- Movement Controls (Mobile) -->
            <div id="movement-controls" class="mobile-controls">
                <div class="d-pad">
                    <button class="move-btn" data-direction="up">↑</button>
                    <button class="move-btn" data-direction="left">←</button>
                    <button class="move-btn" data-direction="right">→</button>
                    <button class="move-btn" data-direction="down">↓</button>
                </div>
                <div class="action-buttons">
                    <button class="action-btn" id="interact-btn">Interagir</button>
                    <button class="action-btn" id="scan-btn">Scanner</button>
                </div>
            </div>
        </div>

        <!-- Modal Windows -->
        <div id="modal-overlay" class="modal-overlay">
            <!-- Inventory Modal -->
            <div id="inventory-modal" class="modal">
                <div class="modal-header">
                    <h3>Inventaire</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="inventory-grid" id="inventory-grid">
                        <!-- Items will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Skills Modal -->
            <div id="skills-modal" class="modal">
                <div class="modal-header">
                    <h3>Arbre de Compétences</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <div class="skill-tree" id="skill-tree">
                        <!-- Skill tree will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Map Modal -->
            <div id="map-modal" class="modal large">
                <div class="modal-header">
                    <h3>Carte du Monde</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-content">
                    <canvas id="world-map-canvas" width="800" height="600"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="js/core/GameConfig.js"></script>
    <script src="js/core/GameState.js"></script>
    <script src="js/systems/SaveSystem.js"></script>
    <script src="js/systems/ChakraSystem.js"></script>
    <script src="js/systems/CombatSystem.js"></script>
    <script src="js/systems/FogOfWar.js"></script>
    <script src="js/scenes/LoadingScene.js"></script>
    <script src="js/scenes/ExplorationScene.js"></script>
    <script src="js/scenes/BattleScene.js"></script>
    <script src="js/scenes/VillageScene.js"></script>
    <script src="js/ui/HUD.js"></script>
    <script src="js/ui/InventoryUI.js"></script>
    <script src="js/ui/SkillTreeUI.js"></script>
    <script src="js/ui/MapUI.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
