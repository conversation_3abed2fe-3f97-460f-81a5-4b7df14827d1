/**
 * Gestion de l'état global du jeu
 * Inspiré de la structure de la Proposition 2 avec les améliorations de la Proposition 1
 */

class GameState {
    constructor() {
        this.initializeDefaultState();
        this.loadFromStorage();
    }

    initializeDefaultState() {
        this.state = {
            // Informations du joueur
            player: {
                name: "",
                clan: "",
                level: 1,
                hp: 100,
                maxHp: 100,
                chakra: 20,
                maxChakra: 20,
                xp: 0,
                xpToNext: 100,
                
                // Position dans le monde
                position: { x: 25, y: 25 },
                currentRegion: "konoha_forest",
                
                // Stats de base
                stats: {
                    attack: 10,
                    defense: 5,
                    speed: 8,
                    stealth: 5
                },
                
                // Équipement
                equipment: {
                    weapon: "basic_kunai",
                    armor: "cloth_vest",
                    accessory: null
                },
                
                // Compétences et jutsu
                skills: {
                    taijutsu: 1,
                    ninjutsu: 1,
                    genjutsu: 1
                },
                
                jutsu: ["basic_attack"], // Jutsu connus
                skillPoints: 0
            },

            // État du monde
            world: {
                currentMap: "konoha_forest",
                exploredTiles: ["25,25", "25,26", "26,25"], // Format "x,y"
                unlockedRegions: ["konoha_forest"],
                
                // Événements du monde
                completedEvents: [],
                activeQuests: [],
                completedQuests: [],
                
                // Donjons et boss
                clearedDungeons: [],
                defeatedBosses: []
            },

            // Inventaire
            inventory: {
                items: [
                    { id: "chakra_pill", quantity: 3 },
                    { id: "healing_salve", quantity: 2 }
                ],
                maxSlots: 20,
                ryo: 100 // Monnaie du jeu
            },

            // Progression et succès
            progress: {
                totalPlayTime: 0,
                enemiesDefeated: 0,
                questsCompleted: 0,
                regionsExplored: 1,
                rank: "Genin",
                
                // Statistiques par clan
                clanReputation: {
                    ryujin: 0,
                    yurei: 0,
                    kaze: 0
                }
            },

            // Paramètres du jeu
            settings: {
                musicVolume: 0.5,
                sfxVolume: 0.8,
                autoSave: true,
                difficulty: "normal",
                showTutorials: true
            },

            // Métadonnées
            meta: {
                version: "1.0.0",
                created: new Date().toISOString(),
                lastSaved: new Date().toISOString(),
                saveSlot: 1
            }
        };
    }

    // Getters pour accès facile aux données
    get player() { return this.state.player; }
    get world() { return this.state.world; }
    get inventory() { return this.state.inventory; }
    get progress() { return this.state.progress; }
    get settings() { return this.state.settings; }

    // Gestion du joueur
    updatePlayerStats(newStats) {
        Object.assign(this.state.player.stats, newStats);
        this.autoSave();
    }

    updatePlayerPosition(x, y) {
        this.state.player.position = { x, y };
        
        // Ajouter la tuile aux zones explorées
        const tileKey = `${x},${y}`;
        if (!this.state.world.exploredTiles.includes(tileKey)) {
            this.state.world.exploredTiles.push(tileKey);
        }
        
        this.autoSave();
    }

    // Gestion de l'expérience et des niveaux
    gainXP(amount) {
        this.state.player.xp += amount;
        
        while (this.state.player.xp >= this.state.player.xpToNext) {
            this.levelUp();
        }
        
        this.autoSave();
    }

    levelUp() {
        const oldLevel = this.state.player.level;
        this.state.player.level++;
        this.state.player.xp -= this.state.player.xpToNext;
        this.state.player.xpToNext = GameConfig.getXPForLevel(this.state.player.level);
        
        // Augmentation des stats
        const statsGain = GameConfig.progression.statsPerLevel;
        this.state.player.maxHp += statsGain.hp;
        this.state.player.hp = this.state.player.maxHp; // Heal complet au level up
        this.state.player.maxChakra += statsGain.chakra;
        this.state.player.chakra = this.state.player.maxChakra;
        this.state.player.stats.attack += statsGain.attack;
        this.state.player.stats.defense += statsGain.defense;
        
        // Points de compétence
        this.state.player.skillPoints++;
        if (GameConfig.progression.bonusSkillPointsAt.includes(this.state.player.level)) {
            this.state.player.skillPoints++;
        }
        
        // Mise à jour du rang
        this.state.progress.rank = GameConfig.getRankForLevel(this.state.player.level).name;
        
        // Débloquer de nouvelles régions
        this.checkRegionUnlocks();
        
        console.log(`Level Up! Niveau ${this.state.player.level} atteint!`);
        this.autoSave();
    }

    // Gestion du chakra
    spendChakra(amount) {
        if (this.state.player.chakra >= amount) {
            this.state.player.chakra -= amount;
            this.autoSave();
            return true;
        }
        return false;
    }

    restoreChakra(amount) {
        this.state.player.chakra = Math.min(
            this.state.player.maxChakra,
            this.state.player.chakra + amount
        );
        this.autoSave();
    }

    // Gestion de l'inventaire
    addItem(itemId, quantity = 1) {
        const existingItem = this.state.inventory.items.find(item => item.id === itemId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            if (this.state.inventory.items.length < this.state.inventory.maxSlots) {
                this.state.inventory.items.push({ id: itemId, quantity });
            } else {
                console.log("Inventaire plein!");
                return false;
            }
        }
        
        this.autoSave();
        return true;
    }

    removeItem(itemId, quantity = 1) {
        const item = this.state.inventory.items.find(item => item.id === itemId);
        
        if (item && item.quantity >= quantity) {
            item.quantity -= quantity;
            
            if (item.quantity <= 0) {
                const index = this.state.inventory.items.indexOf(item);
                this.state.inventory.items.splice(index, 1);
            }
            
            this.autoSave();
            return true;
        }
        
        return false;
    }

    // Vérification des débloquages de régions
    checkRegionUnlocks() {
        for (const [regionId, regionData] of Object.entries(GameConfig.world.regions)) {
            if (regionData.unlockLevel && 
                this.state.player.level >= regionData.unlockLevel &&
                !this.state.world.unlockedRegions.includes(regionId)) {
                
                this.state.world.unlockedRegions.push(regionId);
                console.log(`Nouvelle région débloquée: ${regionData.name}`);
            }
        }
    }

    // Sauvegarde automatique
    autoSave() {
        if (this.state.settings.autoSave) {
            this.saveToStorage();
        }
    }

    // Sauvegarde dans le localStorage
    saveToStorage(slot = null) {
        const saveSlot = slot || this.state.meta.saveSlot;
        this.state.meta.lastSaved = new Date().toISOString();
        
        try {
            const saveData = JSON.stringify(this.state);
            localStorage.setItem(`shinobi_save_${saveSlot}`, saveData);
            console.log(`Jeu sauvegardé dans le slot ${saveSlot}`);
            return true;
        } catch (error) {
            console.error("Erreur lors de la sauvegarde:", error);
            return false;
        }
    }

    // Chargement depuis le localStorage
    loadFromStorage(slot = 1) {
        try {
            const saveData = localStorage.getItem(`shinobi_save_${slot}`);
            if (saveData) {
                const loadedState = JSON.parse(saveData);
                this.state = { ...this.state, ...loadedState };
                this.state.meta.saveSlot = slot;
                console.log(`Jeu chargé depuis le slot ${slot}`);
                return true;
            }
        } catch (error) {
            console.error("Erreur lors du chargement:", error);
        }
        return false;
    }

    // Réinitialisation du jeu
    resetGame() {
        this.initializeDefaultState();
        this.saveToStorage();
    }

    // Export des données pour debug
    exportSave() {
        return JSON.stringify(this.state, null, 2);
    }
}

// Instance globale du GameState
const gameState = new GameState();
