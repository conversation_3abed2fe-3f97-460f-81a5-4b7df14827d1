[{"id": "chakra_pill", "name": "<PERSON><PERSON><PERSON>", "type": "consumable", "rarity": "common", "description": "Restaure 20 points de chakra instantanément", "sprite": "items/chakra_pill.png", "effect": {"type": "restore_chakra", "value": 20}, "stackable": true, "max_stack": 99, "sell_price": 25, "buy_price": 50}, {"id": "healing_salve", "name": "<PERSON>ume de Soin", "type": "consumable", "rarity": "common", "description": "Restaure 30 points de vie", "sprite": "items/healing_salve.png", "effect": {"type": "restore_hp", "value": 30}, "stackable": true, "max_stack": 99, "sell_price": 20, "buy_price": 40}, {"id": "basic_kunai", "name": "Kunai Basique", "type": "weapon", "rarity": "common", "description": "Une arme de lancer standard des ninjas", "sprite": "items/basic_kunai.png", "stats": {"attack": 15, "accuracy": 85}, "requirements": {"level": 1}, "stackable": false, "sell_price": 15, "buy_price": 30}, {"id": "cloth_vest", "name": "<PERSON><PERSON>", "type": "armor", "rarity": "common", "description": "Protection légère pour les ninjas débutants", "sprite": "items/cloth_vest.png", "stats": {"defense": 8, "weight": 2}, "requirements": {"level": 1}, "stackable": false, "sell_price": 25, "buy_price": 50}, {"id": "shuriken", "name": "<PERSON><PERSON><PERSON>", "type": "consumable", "rarity": "common", "description": "<PERSON><PERSON><PERSON> de lancer qui inflige des dégâts à distance", "sprite": "items/shuriken.png", "effect": {"type": "ranged_attack", "damage": 12, "accuracy": 90}, "stackable": true, "max_stack": 50, "sell_price": 5, "buy_price": 10}, {"id": "fire_scroll_fragment", "name": "Fragment de Parch<PERSON>in <PERSON>", "type": "crafting_material", "rarity": "uncommon", "description": "Fragment d'un ancien parchemin de jutsu de feu. Collectez 5 fragments pour créer un parchemin complet.", "sprite": "items/fire_scroll_fragment.png", "stackable": true, "max_stack": 10, "sell_price": 50, "craft_into": "fire_jutsu_scroll"}, {"id": "wind_scroll_fragment", "name": "Fragment de Parchemin <PERSON>", "type": "crafting_material", "rarity": "uncommon", "description": "Fragment d'un ancien parchemin de jutsu de vent", "sprite": "items/wind_scroll_fragment.png", "stackable": true, "max_stack": 10, "sell_price": 50, "craft_into": "wind_jutsu_scroll"}, {"id": "shadow_essence", "name": "Essence d'Ombre", "type": "crafting_material", "rarity": "rare", "description": "Essence pure d'énergie d'ombre, très prisée par les maîtres genjutsu", "sprite": "items/shadow_essence.png", "stackable": true, "max_stack": 5, "sell_price": 100}, {"id": "ninja_headband", "name": "Bandeau Ninja", "type": "accessory", "rarity": "uncommon", "description": "Bandeau d'un ninja renégat, symbole de son ancien village", "sprite": "items/ninja_headband.png", "stats": {"chakra": 10, "prestige": 5}, "requirements": {"level": 3}, "stackable": false, "sell_price": 75, "buy_price": 150}, {"id": "bandit_cloak", "name": "Cape de Bandit", "type": "armor", "rarity": "uncommon", "description": "Cape sombre qui améliore la furtivité", "sprite": "items/bandit_cloak.png", "stats": {"defense": 12, "stealth": 8, "weight": 3}, "requirements": {"level": 2}, "stackable": false, "sell_price": 60, "buy_price": 120}, {"id": "phoenix_robe", "name": "Robe du Phénix", "type": "armor", "rarity": "legendary", "description": "Robe légendaire des maîtres du feu, tissée avec des plumes de phénix", "sprite": "items/phoenix_robe.png", "stats": {"defense": 25, "max_chakra": 30, "fire_resistance": 50, "fire_power": 20}, "requirements": {"level": 15, "clan": "ryujin"}, "stackable": false, "sell_price": 2000, "special_effects": ["fire_immunity", "phoenix_rebirth"]}, {"id": "shadow_fang", "name": "Croc d'<PERSON>mbre", "type": "crafting_material", "rarity": "uncommon", "description": "<PERSON>ro<PERSON> d'un loup d'ombre, imprégné de chakra sombre", "sprite": "items/shadow_fang.png", "stackable": true, "max_stack": 10, "sell_price": 40}, {"id": "poison_sac", "name": "Glande à Poison", "type": "crafting_material", "rarity": "uncommon", "description": "Glande venimeuse d'un scorpion du désert", "sprite": "items/poison_sac.png", "stackable": true, "max_stack": 5, "sell_price": 80}, {"id": "mist_essence", "name": "<PERSON><PERSON><PERSON>", "type": "crafting_material", "rarity": "rare", "description": "Essence condensée de la brume mystique", "sprite": "items/mist_essence.png", "stackable": true, "max_stack": 3, "sell_price": 150}, {"id": "dragon_scale_armor", "name": "Armure d'Écailles de Dragon", "type": "armor", "rarity": "epic", "description": "Armure forgée avec de véritables écailles de dragon", "sprite": "items/dragon_scale_armor.png", "stats": {"defense": 35, "fire_resistance": 40, "max_hp": 50, "weight": 8}, "requirements": {"level": 12, "clan": "ryujin"}, "stackable": false, "sell_price": 1500, "special_effects": ["dragon_might"]}, {"id": "wind_walker_boots", "name": "<PERSON><PERSON> du Marcheur de Vent", "type": "accessory", "rarity": "epic", "description": "Bottes légendaires qui permettent de marcher sur l'air", "sprite": "items/wind_walker_boots.png", "stats": {"speed": 15, "wind_power": 25, "movement_cost": -2}, "requirements": {"level": 10, "clan": "kaze"}, "stackable": false, "sell_price": 1200, "special_effects": ["air_walk", "wind_dash"]}, {"id": "shadow_cloak", "name": "Cape d'Ombre", "type": "armor", "rarity": "epic", "description": "Cape qui rend son porteur presque invisible", "sprite": "items/shadow_cloak.png", "stats": {"defense": 20, "stealth": 25, "shadow_power": 20, "weight": 1}, "requirements": {"level": 10, "clan": "yurei"}, "stackable": false, "sell_price": 1300, "special_effects": ["shadow_step", "invisibility"]}, {"id": "chakra_crystal", "name": "<PERSON><PERSON><PERSON>", "type": "consumable", "rarity": "rare", "description": "Cristal pur qui restaure complètement le chakra", "sprite": "items/chakra_crystal.png", "effect": {"type": "restore_chakra", "value": "full"}, "stackable": true, "max_stack": 5, "sell_price": 200, "buy_price": 400}, {"id": "elixir_of_life", "name": "<PERSON><PERSON><PERSON>", "type": "consumable", "rarity": "legendary", "description": "Potion légendaire qui restaure complètement la vie et le chakra", "sprite": "items/elixir_of_life.png", "effect": {"type": "full_restore", "hp": "full", "chakra": "full"}, "stackable": true, "max_stack": 1, "sell_price": 1000, "buy_price": 2000}, {"id": "beast_meat", "name": "Viande de Bête", "type": "consumable", "rarity": "common", "description": "Viande nutritive qui restaure un peu de vie", "sprite": "items/beast_meat.png", "effect": {"type": "restore_hp", "value": 15}, "stackable": true, "max_stack": 20, "sell_price": 8, "buy_price": 15}, {"id": "skill_point_scroll", "name": "<PERSON><PERSON><PERSON><PERSON> de Compétence", "type": "consumable", "rarity": "rare", "description": "Parchemin ancien qui accorde un point de compétence", "sprite": "items/skill_point_scroll.png", "effect": {"type": "gain_skill_point", "value": 1}, "stackable": true, "max_stack": 10, "sell_price": 500, "buy_price": 1000}]