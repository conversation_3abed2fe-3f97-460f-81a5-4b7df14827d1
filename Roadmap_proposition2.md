# Document Technique Complet : Ninja Path - Shinobi Chronicles

## 1. Architecture Technique
```javascript
// Structure de sauvegarde
const gameState = {
  player: { 
    level: 1, 
    hp: 100, 
    maxHp: 100,
    chakra: 20, 
    maxChakra: 20,
    xp: 0,
    xpToNext: 100,
    position: {x: 25, y: 25},
    equipment: {
      weapon: "kunai",
      armor: "cloth_vest"
    }
  },
  map: {
    current: "leafhaven_forest",
    explored: ["25,25", "25,26", "26,25"] 
  },
  inventory: [
    {id: "chakra_pill", quantity: 3},
    {id: "healing_salve", quantity: 2}
  ],
  lastUpdate: "2025-06-04T12:00:00Z"
};

// Classes de base
class Enemy {
  constructor(id, name, hp, attack, defense, element, xp, loot) {
    this.id = id;
    this.name = name;
    this.hp = hp;
    this.attack = attack;
    this.defense = defense;
    this.element = element;
    this.xp = xp;
    this.loot = loot;
  }
}
2. Systèmes Clés
a. Carte et Exploration
Structure de tuile :

json
{
  "type": "forest",
  "encounterChance": 0.7,
  "encounterList": ["wild_boar", "bandit"],
  "event": {
    "type": "chest",
    "item": "chakra_pill",
    "quantity": 2
  }
}
Mécanique de mouvement :

javascript
function move(direction) {
  if (gameState.player.chakra < 1) return showMessage("Pas assez de chakra!");
  
  // Mise à jour position
  const newPos = calculateNewPosition(direction);
  gameState.player.position = newPos;
  
  // Ajouter aux cases explorées
  const posKey = `${newPos.x},${newPos.y}`;
  if (!gameState.map.explored.includes(posKey)) {
    gameState.map.explored.push(posKey);
  }
  
  // Vérifier les événements
  checkTileEvent(newPos);
  saveGame();
}
b. Système de Combat
Flow de combat :

Diagram
Code
graph TD
  A[Rencontre Ennemi] --> B{Afficher Menu}
  B --> C[Attaque]
  B --> D[Jutsu]
  B --> E[Objets]
  B --> F[Fuite]
  C --> G[Calcul Dégâts]
  G --> H[Vérifier Victoire]
  H -->|Non| I[Tour Ennemi]
  I --> J[Calcul Dégâts Ennemi]
  J --> K[Vérifier Défaite]








Formule de dégâts :

javascript
function calculateDamage(attacker, defender, element) {
  const baseDamage = attacker.attack - defender.defense;
  const multiplier = getElementMultiplier(attacker.element, defender.element);
  return Math.max(1, Math.floor(baseDamage * multiplier));
}

function getElementMultiplier(attackElement, defenseElement) {
  const weaknesses = {
    fire: "wind",
    wind: "earth",
    earth: "lightning",
    lightning: "water",
    water: "fire"
  };
  return weaknesses[attackElement] === defenseElement ? 1.5 : 1;
}
c. Gestion d'Énergie
javascript
function updateChakra() {
  const now = new Date();
  const lastUpdate = new Date(gameState.lastUpdate);
  const minutesPassed = Math.floor((now - lastUpdate) / (1000 * 60));
  const chakraRegen = Math.floor(minutesPassed / 5);
  
  if (chakraRegen > 0) {
    gameState.player.chakra = Math.min(
      gameState.player.maxChakra,
      gameState.player.chakra + chakraRegen
    );
    gameState.lastUpdate = now.toISOString();
    saveGame();
  }
}

// Exécuter toutes les minutes
setInterval(updateChakra, 60000);
3. Bases de Données
enemies.json
json
[
  {
    "id": "wild_boar",
    "name": "Sanglier Sauvage",
    "hp": 30,
    "attack": 8,
    "defense": 5,
    "element": "earth",
    "xp": 25,
    "loot": [
      {"id": "boar_tusk", "chance": 0.7, "quantity": 1},
      {"id": "healing_salve", "chance": 0.3, "quantity": 1}
    ]
  },
  {
    "id": "sand_ninja",
    "name": "Ninja du Sable",
    "hp": 60,
    "attack": 15,
    "defense": 10,
    "element": "wind",
    "xp": 50,
    "loot": [
      {"id": "shuriken", "chance": 0.5, "quantity": 3},
      {"id": "chakra_pill", "chance": 0.4, "quantity": 2}
    ]
  }
]
items.json
json
[
  {
    "id": "chakra_pill",
    "name": "Pilule de Chakra",
    "type": "consumable",
    "effect": "restore_chakra",
    "value": 10,
    "description": "Restaure 10 points de chakra"
  },
  {
    "id": "phoenix_robe",
    "name": "Robe du Phénix",
    "type": "armor",
    "stats": {"defense": 15, "maxChakra": 10},
    "description": "Robe légendaire des maîtres du feu"
  }
]
4. Interface Utilisateur
Écran d'Exploration
html
<div class="game-container">
  <div class="fog-overlay"></div>
  
  <div class="player-view">
    <div class="tile forest"></div>
  </div>
  
  <div class="stats-panel">
    <div>Niveau: <span id="level">1</span></div>
    <div>Chakra: <span id="chakra">20/20</span></div>
    <div>HP: <span id="hp">100/100</span></div>
  </div>
  
  <div class="controls">
    <button onclick="move('north')">↑</button>
    <button onclick="move('west')">←</button>
    <button onclick="move('east')">→</button>
    <button onclick="move('south')">↓</button>
  </div>
</div>
5. Ce que l'IA Va Générer
Composant	Technologie	Sortie Attendue
Moteur de carte	Phaser 3	Grille 100x100 avec FOG
Système de combat	JavaScript	Flow tour-par-tour complet
Gestion d'objets	JSON + JS	Inventaire et utilisation
Système de sauvegarde	localStorage	Persistance des données
UI de base	HTML/CSS	Menus interactifs
Génération de map	Algorithmes	Placement procédural d'événements
6. Partie Humaine Obligatoire
Tâche	Description	Outils Recommandés
Création d'assets	Sprites, tilesets, effets visuels	Aseprite, Photoshop
Sound Design	SFX, musique d'ambiance	BFXR, Audacity
Animation des Jutsus	Effets spéciaux pour techniques	Spine, After Effects
Écriture du Lore	Dialogues, histoires des villages	Google Docs
Équilibrage du gameplay	Ajustement XP, difficulté, économie	Playtesting
Design des interfaces	UI/UX final et icônes	Figma, Illustrator
Optimisation mobile	Adaptation pour écrans tactiles	Chrome DevTools
7. Workflow Recommandé
Phase IA Initiale (MVP)

prompt
"Génère le code pour un système de mouvement sur grille 50x50 avec Phaser 3, 
incluant le fog of war et la gestion de chakra"
Phase Humaine (Contenu)

Créer les sprites du joueur et 5 ennemis de base

Concevoir les tilesets pour la forêt de Leafhaven

Équilibrer les stats de départ

Phase IA Avancée

prompt
"Implémente le système de combat tour-par-tour avec élémental rock-paper-scissors"
Phase Polissage Humain

Ajouter des animations de combat

Affiner les valeurs d'équilibrage

Créer des événements spéciaux uniques

8. Exemple de Prompt Complet pour IA
prompt
"Génère un système complet de progression de personnage en JavaScript avec:

- Niveaux de 1 à 50
- Formule XP: xpToNext = niveau_actuel * 100
- Statistiques qui augmentent par niveau:
  HP: +10 par niveau
  Chakra: +5 par niveau
  Attaque: +2 par niveau
  Défense: +1 par niveau
- Points de compétence à attribuer tous les 5 niveaux
- Interface de mise à niveau montrant les nouvelles stats

Utilise la structure gameState existante et prévois des fonctions:
- gainXP(amount)
- checkLevelUp()
- applyLevelUp()
Fichiers à Créer
game.js - Logique principale du jeu

combat.js - Système de combat

maps/leafhaven_forest.json - Données de la première map

data/enemies.json - Base de données des ennemis

data/items.json - Base de données des objets

index.html - Point d'entrée principal

styles.css - Styles de l'interface