# 🥷 Shinobi Chronicles - Shadow of the Ninja

Un jeu de navigateur inspiré de l'univers Naruto, combinant exploration, combat et progression de clan.

## 🎯 Vue d'ensemble

Shinobi Chronicles est un RPG 2D basé sur navigateur qui combine les meilleures idées de deux propositions de conception :

- **Proposition 1** : Lore riche, système de progression complexe, donjons et boss
- **Proposition 2** : Architecture technique solide, approche pragmatique, développement rapide

### Approche Hybride

Le projet utilise une approche hybride qui permet :
1. **MVP rapide** (2-3 mois) avec les mécaniques de base
2. **Enrichissement progressif** avec le lore et les fonctionnalités avancées

## 🏗️ Architecture du Projet

```
Shinobi/
├── index.html                 # Point d'entrée principal
├── package.json              # Configuration npm
├── README.md                 # Documentation
│
├── js/                       # Code JavaScript
│   ├── core/                 # Modules principaux
│   │   ├── GameConfig.js     # Configuration globale
│   │   └── GameState.js      # Gestion d'état
│   │
│   ├── systems/              # Systèmes de jeu
│   │   ├── SaveSystem.js     # Sauvegarde/chargement
│   │   ├── ChakraSystem.js   # Gestion du chakra
│   │   ├── CombatSystem.js   # Système de combat
│   │   └── FogOfWar.js       # Brouillard de guerre
│   │
│   ├── scenes/               # Scènes Phaser (à créer)
│   │   ├── LoadingScene.js
│   │   ├── ExplorationScene.js
│   │   ├── BattleScene.js
│   │   └── VillageScene.js
│   │
│   ├── ui/                   # Interface utilisateur (à créer)
│   │   ├── HUD.js
│   │   ├── InventoryUI.js
│   │   ├── SkillTreeUI.js
│   │   └── MapUI.js
│   │
│   └── main.js               # Point d'entrée JavaScript
│
├── styles/                   # Feuilles de style
│   ├── main.css              # Styles principaux
│   ├── ui.css                # Styles d'interface (à créer)
│   └── combat.css            # Styles de combat (à créer)
│
├── data/                     # Données de jeu (JSON)
│   ├── enemies.json          # Base de données des ennemis
│   ├── items.json            # Base de données des objets
│   ├── jutsu.json            # Base de données des jutsu
│   ├── quests.json           # Quêtes (à créer)
│   └── maps/                 # Données des cartes (à créer)
│
└── assets/                   # Ressources (à créer)
    ├── images/
    │   ├── sprites/
    │   ├── tilesets/
    │   └── ui/
    └── audio/
        ├── music/
        └── sfx/
```

## 🎮 Fonctionnalités Principales

### ✅ Implémentées
- **Configuration du jeu** : Système modulaire avec GameConfig
- **Gestion d'état** : GameState avec sauvegarde automatique
- **Système de chakra** : Régénération, coûts, bonus de clan
- **Système de combat** : Tour par tour avec éléments et jutsu
- **Fog of War** : Exploration progressive avec révélation de carte
- **Système de sauvegarde** : Multi-slots avec compression et intégrité
- **Interface de base** : Menus, création de personnage, HUD

### 🚧 En développement
- **Scènes Phaser** : Exploration, combat, villages
- **Interface utilisateur** : Inventaire, arbre de compétences, carte
- **Système de quêtes** : Missions principales et secondaires
- **Assets visuels** : Sprites, tilesets, animations
- **Audio** : Musique et effets sonores

## 🏮 Système de Clans

### Clan Ryūjin (Feu)
- **Élément** : Feu
- **Bonus** : +2 Attaque, +20% Résistance au feu
- **Jutsu de départ** : Tranche du Dragon
- **Couleur** : Rouge (#e74c3c)

### Clan Yūrei (Ombre)
- **Élément** : Ombre
- **Bonus** : +3 Furtivité, +30% Puissance Genjutsu
- **Jutsu de départ** : Clone d'Ombre
- **Couleur** : Violet (#9b59b6)

### Clan Kaze (Vent)
- **Élément** : Vent
- **Bonus** : +2 Vitesse, +25% Puissance du Vent
- **Jutsu de départ** : Lame de Vent
- **Couleur** : Bleu (#3498db)

## 🗺️ Régions du Monde

1. **Forêt de Konoha** (Niv. 1-3) - Zone de départ
2. **Forêt des Murmures** (Niv. 3-5) - Première expansion
3. **Terres Arides** (Niv. 5-8) - Désert et donjons
4. **Marécages Cachés** (Niv. 8-10) - Zone mystique
5. **Sanctuaire de la Montagne** (Niv. 10+) - Boss final

## ⚔️ Système de Combat

### Types d'Attaque
- **Taijutsu** : Attaques physiques (coût chakra faible)
- **Ninjutsu** : Techniques élémentaires (coût chakra moyen)
- **Genjutsu** : Illusions et contrôle (coût chakra élevé)

### Éléments et Relations
- **Feu** > Vent > Terre > Foudre > Eau > Feu
- **Ombre** et **Lumière** s'opposent mutuellement

## 🚀 Installation et Lancement

### Prérequis
- Node.js (version 14+)
- Navigateur moderne (Chrome, Firefox, Safari, Edge)

### Installation
```bash
# Cloner le projet
git clone [URL_DU_REPO]
cd Shinobi

# Installer les dépendances
npm install

# Lancer le serveur de développement
npm run dev
```

### Lancement rapide
```bash
# Serveur HTTP simple
npm start
```

Le jeu sera accessible à l'adresse : `http://localhost:8080`

## 🛠️ Développement

### Structure des Données

#### GameState
```javascript
{
  player: {
    name: "Ninja",
    clan: "ryujin",
    level: 1,
    hp: 100,
    chakra: 20,
    position: {x: 25, y: 25},
    stats: { attack: 10, defense: 5, speed: 8 }
  },
  world: {
    currentMap: "konoha_forest",
    exploredTiles: ["25,25", "25,26"],
    unlockedRegions: ["konoha_forest"]
  },
  inventory: {
    items: [
      {id: "chakra_pill", quantity: 3}
    ],
    ryo: 100
  }
}
```

#### Configuration des Ennemis
```javascript
{
  "id": "wild_boar",
  "name": "Sanglier Sauvage",
  "level": 1,
  "hp": 30,
  "attack": 8,
  "element": "earth",
  "loot": [
    {"id": "boar_tusk", "chance": 0.7, "quantity": 1}
  ]
}
```

### Ajout de Contenu

#### Nouvel Ennemi
1. Ajouter l'entrée dans `data/enemies.json`
2. Créer le sprite dans `assets/images/enemies/`
3. Configurer les zones d'apparition

#### Nouveau Jutsu
1. Ajouter l'entrée dans `data/jutsu.json`
2. Implémenter la logique dans `CombatSystem.js`
3. Ajouter les animations et effets

#### Nouvelle Région
1. Créer le fichier de carte dans `data/maps/`
2. Ajouter la configuration dans `GameConfig.js`
3. Créer les tilesets et sprites

## 🎨 Assets Requis

### Sprites (32x32px)
- **Joueur** : 8 frames d'animation (marche, attaque, jutsu)
- **Ennemis** : 4 frames minimum (idle, attaque)
- **Objets** : Icônes d'inventaire
- **Interface** : Boutons, barres, cadres

### Tilesets (32x32px)
- **Forêt** : Arbres, herbe, rochers, chemins
- **Désert** : Sable, cactus, ruines
- **Marécages** : Eau, roseaux, brume
- **Montagne** : Rochers, temples, neige

### Audio
- **Musique** : Thèmes par région (format OGG)
- **SFX** : Attaques, jutsu, interface (format WAV)

## 🧪 Tests et Debug

### Mode Debug
Activer dans `GameConfig.js` :
```javascript
debug: {
  enabled: true,
  godMode: true,
  unlockAllRegions: true,
  startLevel: 10
}
```

### Console Commands
```javascript
// Gagner de l'XP
gameState.gainXP(1000);

// Ajouter des objets
gameState.addItem("chakra_pill", 10);

// Révéler toute la carte
fogOfWar.revealAllMap();

// Téléportation
gameState.updatePlayerPosition(50, 50);
```

## 📈 Roadmap de Développement

### Phase 1 : MVP (Mois 1-2)
- [x] Structure technique de base
- [x] Systèmes principaux (chakra, combat, sauvegarde)
- [ ] Scène d'exploration fonctionnelle
- [ ] Combat basique
- [ ] Interface utilisateur minimale

### Phase 2 : Contenu (Mois 3-4)
- [ ] 2 régions complètes (Konoha + Forêt des Murmures)
- [ ] 10 ennemis différents
- [ ] Système de quêtes
- [ ] Arbre de compétences
- [ ] Premier donjon

### Phase 3 : Polish (Mois 5-6)
- [ ] Toutes les régions
- [ ] Boss et donjons
- [ ] Audio complet
- [ ] Optimisations
- [ ] Tests et équilibrage

## 🤝 Contribution

### Guidelines
1. Respecter l'architecture modulaire
2. Commenter le code en français
3. Tester les nouvelles fonctionnalités
4. Maintenir la compatibilité avec les sauvegardes

### Branches
- `main` : Version stable
- `development` : Développement actif
- `feature/*` : Nouvelles fonctionnalités

## 📄 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.

## 🙏 Remerciements

- Inspiration : Univers Naruto de Masashi Kishimoto
- Moteur : Phaser 3 Framework
- Assets : Communauté open source (à créditer)

---

**Que l'aventure ninja commence ! 🥷✨**
