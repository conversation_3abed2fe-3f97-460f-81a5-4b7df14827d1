/**
 * Système Fog of War pour Shinobi Chronicles
 * Inspiré de la Proposition 1 avec optimisations
 */

class FogOfWar {
    constructor(scene) {
        this.scene = scene;
        this.mapWidth = GameConfig.world.mapWidth;
        this.mapHeight = GameConfig.world.mapHeight;
        this.tileSize = GameConfig.world.tileSize;
        this.revealRadius = GameConfig.world.fogOfWarRadius;
        
        // Grille de visibilité (true = visible, false = caché)
        this.visibilityGrid = this.initializeVisibilityGrid();
        
        // Texture de fog
        this.fogTexture = null;
        this.fogRenderTexture = null;
        
        // Cache pour optimisation
        this.lastPlayerPosition = { x: -1, y: -1 };
        this.revealedTilesCache = new Set();
        
        this.initializeFogSystem();
    }

    // Initialise la grille de visibilité
    initializeVisibilityGrid() {
        const grid = [];
        for (let y = 0; y < this.mapHeight; y++) {
            grid[y] = [];
            for (let x = 0; x < this.mapWidth; x++) {
                grid[y][x] = false; // Tout est caché au début
            }
        }
        return grid;
    }

    // Initialise le système de fog
    initializeFogSystem() {
        this.createFogTexture();
        this.createFogRenderTexture();
        this.loadExploredTiles();
    }

    // Crée la texture de fog
    createFogTexture() {
        // Créer une texture noire pour le fog
        this.scene.textures.generate('fog_tile', {
            data: ['0'],
            pixelWidth: this.tileSize,
            pixelHeight: this.tileSize
        });
    }

    // Crée la RenderTexture pour le fog
    createFogRenderTexture() {
        const mapPixelWidth = this.mapWidth * this.tileSize;
        const mapPixelHeight = this.mapHeight * this.tileSize;
        
        this.fogRenderTexture = this.scene.add.renderTexture(
            0, 0, mapPixelWidth, mapPixelHeight
        ).setDepth(1000); // Au-dessus de tout
        
        // Remplir entièrement de noir au début
        this.fogRenderTexture.fill(0x000000, 1.0);
    }

    // Charge les tuiles déjà explorées depuis la sauvegarde
    loadExploredTiles() {
        const exploredTiles = gameState.world.exploredTiles || [];
        
        exploredTiles.forEach(tileKey => {
            const [x, y] = tileKey.split(',').map(Number);
            if (this.isValidTile(x, y)) {
                this.visibilityGrid[y][x] = true;
                this.revealedTilesCache.add(tileKey);
            }
        });
        
        // Mettre à jour la texture de fog
        this.updateFogTexture();
    }

    // Met à jour le fog autour du joueur
    updateFogAroundPlayer(playerX, playerY) {
        const tileX = Math.floor(playerX / this.tileSize);
        const tileY = Math.floor(playerY / this.tileSize);
        
        // Optimisation : ne pas recalculer si le joueur n'a pas changé de tuile
        if (tileX === this.lastPlayerPosition.x && tileY === this.lastPlayerPosition.y) {
            return;
        }
        
        this.lastPlayerPosition = { x: tileX, y: tileY };
        
        // Révéler les tuiles autour du joueur
        const newlyRevealed = this.revealTilesInRadius(tileX, tileY, this.revealRadius);
        
        // Mettre à jour la texture seulement si de nouvelles tuiles ont été révélées
        if (newlyRevealed.length > 0) {
            this.updateFogTextureForTiles(newlyRevealed);
            this.saveNewlyExploredTiles(newlyRevealed);
        }
    }

    // Révèle les tuiles dans un rayon donné
    revealTilesInRadius(centerX, centerY, radius) {
        const newlyRevealed = [];
        
        for (let dy = -radius; dy <= radius; dy++) {
            for (let dx = -radius; dx <= radius; dx++) {
                const x = centerX + dx;
                const y = centerY + dy;
                
                // Vérifier si la tuile est dans les limites
                if (!this.isValidTile(x, y)) continue;
                
                // Calculer la distance (Manhattan ou Euclidienne)
                const distance = this.calculateDistance(dx, dy);
                
                if (distance <= radius && !this.visibilityGrid[y][x]) {
                    this.visibilityGrid[y][x] = true;
                    newlyRevealed.push({ x, y });
                    
                    const tileKey = `${x},${y}`;
                    this.revealedTilesCache.add(tileKey);
                }
            }
        }
        
        return newlyRevealed;
    }

    // Calcule la distance (peut être Manhattan ou Euclidienne)
    calculateDistance(dx, dy) {
        // Distance de Manhattan (plus rapide)
        return Math.abs(dx) + Math.abs(dy);
        
        // Alternative : Distance Euclidienne
        // return Math.sqrt(dx * dx + dy * dy);
    }

    // Met à jour la texture de fog pour des tuiles spécifiques
    updateFogTextureForTiles(tiles) {
        tiles.forEach(tile => {
            const worldX = tile.x * this.tileSize;
            const worldY = tile.y * this.tileSize;
            
            // Effacer (rendre transparent) la zone de fog
            this.fogRenderTexture.erase('fog_tile', worldX, worldY);
        });
    }

    // Met à jour toute la texture de fog (utilisé au chargement)
    updateFogTexture() {
        // Remplir de noir
        this.fogRenderTexture.fill(0x000000, 1.0);
        
        // Effacer les zones révélées
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                if (this.visibilityGrid[y][x]) {
                    const worldX = x * this.tileSize;
                    const worldY = y * this.tileSize;
                    this.fogRenderTexture.erase('fog_tile', worldX, worldY);
                }
            }
        }
    }

    // Sauvegarde les nouvelles tuiles explorées
    saveNewlyExploredTiles(newTiles) {
        newTiles.forEach(tile => {
            const tileKey = `${tile.x},${tile.y}`;
            if (!gameState.world.exploredTiles.includes(tileKey)) {
                gameState.world.exploredTiles.push(tileKey);
            }
        });
        
        // Sauvegarder automatiquement
        gameState.autoSave();
    }

    // Vérifie si une tuile est valide
    isValidTile(x, y) {
        return x >= 0 && x < this.mapWidth && y >= 0 && y < this.mapHeight;
    }

    // Vérifie si une tuile est visible
    isTileVisible(x, y) {
        if (!this.isValidTile(x, y)) return false;
        return this.visibilityGrid[y][x];
    }

    // Révèle une zone spécifique (pour les événements spéciaux)
    revealArea(centerX, centerY, radius) {
        const revealed = this.revealTilesInRadius(centerX, centerY, radius);
        if (revealed.length > 0) {
            this.updateFogTextureForTiles(revealed);
            this.saveNewlyExploredTiles(revealed);
        }
        return revealed.length;
    }

    // Révèle toute la carte (pour debug ou objets spéciaux)
    revealAllMap() {
        const allTiles = [];
        
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                if (!this.visibilityGrid[y][x]) {
                    this.visibilityGrid[y][x] = true;
                    allTiles.push({ x, y });
                }
            }
        }
        
        if (allTiles.length > 0) {
            this.updateFogTexture(); // Reconstruire entièrement
            this.saveNewlyExploredTiles(allTiles);
        }
        
        console.log("Toute la carte a été révélée!");
    }

    // Cache une zone (pour des événements spéciaux comme des illusions)
    hideTiles(tiles) {
        tiles.forEach(tile => {
            if (this.isValidTile(tile.x, tile.y)) {
                this.visibilityGrid[tile.y][tile.x] = false;
                
                // Remettre le fog sur cette tuile
                const worldX = tile.x * this.tileSize;
                const worldY = tile.y * this.tileSize;
                this.fogRenderTexture.fill(0x000000, 1.0, worldX, worldY, this.tileSize, this.tileSize);
            }
        });
    }

    // Obtient les statistiques d'exploration
    getExplorationStats() {
        const totalTiles = this.mapWidth * this.mapHeight;
        const exploredTiles = this.revealedTilesCache.size;
        const explorationPercentage = (exploredTiles / totalTiles) * 100;
        
        return {
            totalTiles,
            exploredTiles,
            explorationPercentage: Math.round(explorationPercentage * 100) / 100,
            hiddenTiles: totalTiles - exploredTiles
        };
    }

    // Obtient les tuiles visibles autour d'une position
    getVisibleTilesAround(centerX, centerY, radius) {
        const visibleTiles = [];
        
        for (let dy = -radius; dy <= radius; dy++) {
            for (let dx = -radius; dx <= radius; dx++) {
                const x = centerX + dx;
                const y = centerY + dy;
                
                if (this.isValidTile(x, y) && this.isTileVisible(x, y)) {
                    visibleTiles.push({ x, y });
                }
            }
        }
        
        return visibleTiles;
    }

    // Effet de révélation progressive (animation)
    animateReveal(tiles, duration = 1000) {
        const delay = duration / tiles.length;
        
        tiles.forEach((tile, index) => {
            setTimeout(() => {
                this.updateFogTextureForTiles([tile]);
                
                // Effet visuel optionnel
                this.createRevealEffect(tile.x * this.tileSize, tile.y * this.tileSize);
            }, index * delay);
        });
    }

    // Crée un effet visuel de révélation
    createRevealEffect(worldX, worldY) {
        // Créer un petit effet de particules ou d'animation
        const effect = this.scene.add.circle(
            worldX + this.tileSize / 2,
            worldY + this.tileSize / 2,
            this.tileSize / 4,
            0xffffff,
            0.5
        ).setDepth(999);
        
        // Animation de disparition
        this.scene.tweens.add({
            targets: effect,
            alpha: 0,
            scale: 2,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
            }
        });
    }

    // Réinitialise le fog (pour nouvelle partie)
    resetFog() {
        // Réinitialiser la grille
        this.visibilityGrid = this.initializeVisibilityGrid();
        this.revealedTilesCache.clear();
        this.lastPlayerPosition = { x: -1, y: -1 };
        
        // Remplir la texture de fog
        this.fogRenderTexture.fill(0x000000, 1.0);
        
        console.log("Fog of War réinitialisé");
    }

    // Optimisation : mise à jour par chunks
    updateFogByChunks(chunkSize = 10) {
        // Cette méthode peut être utilisée pour de très grandes cartes
        // en divisant la mise à jour en plusieurs frames
        console.log("Mise à jour du fog par chunks (optimisation)");
    }

    // Sauvegarde l'état du fog
    saveFogState() {
        return {
            exploredTiles: Array.from(this.revealedTilesCache),
            explorationStats: this.getExplorationStats()
        };
    }

    // Charge l'état du fog
    loadFogState(fogState) {
        if (fogState && fogState.exploredTiles) {
            this.revealedTilesCache = new Set(fogState.exploredTiles);
            this.loadExploredTiles();
        }
    }

    // Nettoyage
    destroy() {
        if (this.fogRenderTexture) {
            this.fogRenderTexture.destroy();
        }
        
        this.visibilityGrid = null;
        this.revealedTilesCache.clear();
    }
}

// Export pour utilisation dans les scènes
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FogOfWar;
}
