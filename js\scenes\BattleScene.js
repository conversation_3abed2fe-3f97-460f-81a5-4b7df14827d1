/**
 * Scène de combat pour Shinobi Chronicles
 * Gère les combats tour par tour avec interface dédiée
 */

class BattleScene extends Phaser.Scene {
    constructor() {
        super({ key: 'BattleScene' });
        
        // État du combat
        this.enemy = null;
        this.battleData = null;
        this.isPlayerTurn = true;
        this.actionSelected = false;
        
        // Interface de combat
        this.battleUI = null;
        this.playerSprite = null;
        this.enemySprite = null;
        this.battleLog = [];
        
        // Animations et effets
        this.effectsContainer = null;
        this.isAnimating = false;
    }

    init(data) {
        // Recevoir les données de l'ennemi depuis ExplorationScene
        this.enemy = data.enemy;
        console.log(`⚔️ Combat initié contre: ${this.enemy.name}`);
    }

    create() {
        // Créer l'arrière-plan de combat
        this.createBackground();
        
        // Initialiser le combat dans le système
        this.battleData = combatSystem.startBattle(this.enemy, 'random');
        
        // Créer l'interface de combat
        this.createBattleUI();
        
        // Créer les sprites des combattants
        this.createCombatants();
        
        // Créer le container pour les effets
        this.effectsContainer = this.add.container(0, 0);
        this.effectsContainer.setDepth(1000);
        
        // Démarrer le premier tour
        this.startTurn();
        
        console.log('✅ Scène de combat initialisée');
    }

    createBackground() {
        const { width, height } = this.cameras.main;
        
        // Arrière-plan dégradé
        const bg = this.add.graphics();
        bg.fillGradientStyle(0x2c3e50, 0x2c3e50, 0x34495e, 0x34495e);
        bg.fillRect(0, 0, width, height);
        
        // Ajouter quelques éléments décoratifs
        for (let i = 0; i < 10; i++) {
            const particle = this.add.circle(
                Phaser.Math.Between(0, width),
                Phaser.Math.Between(0, height),
                Phaser.Math.Between(2, 5),
                0x3498db,
                0.3
            );
            
            this.tweens.add({
                targets: particle,
                alpha: 0.1,
                duration: Phaser.Math.Between(2000, 4000),
                yoyo: true,
                repeat: -1
            });
        }
    }

    createBattleUI() {
        const { width, height } = this.cameras.main;
        
        // Panel principal de l'interface
        this.battleUI = this.add.container(0, height - 200);
        this.battleUI.setDepth(500);
        
        // Fond du panel
        const uiBackground = this.add.rectangle(width/2, 100, width, 200, 0x2c3e50, 0.9);
        uiBackground.setStrokeStyle(2, 0x3498db);
        this.battleUI.add(uiBackground);
        
        // Informations du joueur (gauche)
        this.createPlayerInfo();
        
        // Informations de l'ennemi (droite)
        this.createEnemyInfo();
        
        // Menu d'actions (centre)
        this.createActionMenu();
        
        // Log de combat (haut)
        this.createBattleLog();
    }

    createPlayerInfo() {
        const player = this.battleData.player;
        const playerInfo = this.add.container(100, 50);
        
        // Nom du joueur
        const playerName = this.add.text(0, -60, player.name, {
            fontSize: '18px',
            color: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        
        // Barre de HP
        const hpBg = this.add.rectangle(0, -30, 150, 12, 0x333333);
        const hpBar = this.add.rectangle(-75, -30, 0, 8, 0xe74c3c);
        hpBar.setOrigin(0, 0.5);
        
        // Barre de Chakra
        const chakraBg = this.add.rectangle(0, -10, 150, 12, 0x333333);
        const chakraBar = this.add.rectangle(-75, -10, 0, 8, 0x3498db);
        chakraBar.setOrigin(0, 0.5);
        
        // Textes des stats
        const hpText = this.add.text(0, -30, `${player.hp}/${player.maxHp}`, {
            fontSize: '10px',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        const chakraText = this.add.text(0, -10, `${player.chakra}/${player.maxChakra}`, {
            fontSize: '10px',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        playerInfo.add([playerName, hpBg, hpBar, chakraBg, chakraBar, hpText, chakraText]);
        this.battleUI.add(playerInfo);
        
        // Stocker les références pour les mises à jour
        this.playerHPBar = hpBar;
        this.playerChakraBar = chakraBar;
        this.playerHPText = hpText;
        this.playerChakraText = chakraText;
    }

    createEnemyInfo() {
        const enemy = this.battleData.enemy;
        const enemyInfo = this.add.container(this.cameras.main.width - 100, 50);
        
        // Nom de l'ennemi
        const enemyName = this.add.text(0, -60, enemy.name, {
            fontSize: '18px',
            color: '#ff6b6b',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        
        // Barre de HP
        const hpBg = this.add.rectangle(0, -30, 150, 12, 0x333333);
        const hpBar = this.add.rectangle(75, -30, 0, 8, 0xe74c3c);
        hpBar.setOrigin(1, 0.5);
        
        // Niveau
        const levelText = this.add.text(0, -10, `Niveau ${enemy.level}`, {
            fontSize: '12px',
            color: '#feca57'
        }).setOrigin(0.5);
        
        // Texte HP
        const hpText = this.add.text(0, -30, `${enemy.currentHp}/${enemy.maxHp}`, {
            fontSize: '10px',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        enemyInfo.add([enemyName, hpBg, hpBar, levelText, hpText]);
        this.battleUI.add(enemyInfo);
        
        // Stocker les références
        this.enemyHPBar = hpBar;
        this.enemyHPText = hpText;
    }

    createActionMenu() {
        const { width } = this.cameras.main;
        this.actionMenu = this.add.container(width/2, 50);
        
        // Boutons d'action
        const actions = [
            { text: 'Attaquer', action: 'attack', color: 0xe74c3c },
            { text: 'Jutsu', action: 'jutsu', color: 0x3498db },
            { text: 'Objets', action: 'items', color: 0x27ae60 },
            { text: 'Fuir', action: 'flee', color: 0x95a5a6 }
        ];
        
        this.actionButtons = [];
        
        actions.forEach((actionData, index) => {
            const x = (index - 1.5) * 80;
            
            const button = this.add.rectangle(x, 0, 70, 30, actionData.color, 0.8);
            button.setStrokeStyle(2, 0xffffff);
            button.setInteractive();
            
            const text = this.add.text(x, 0, actionData.text, {
                fontSize: '12px',
                color: '#ffffff',
                fontWeight: 'bold'
            }).setOrigin(0.5);
            
            // Événements de bouton
            button.on('pointerover', () => {
                button.setAlpha(1);
                button.setScale(1.1);
            });
            
            button.on('pointerout', () => {
                button.setAlpha(0.8);
                button.setScale(1);
            });
            
            button.on('pointerdown', () => {
                this.selectAction(actionData.action);
            });
            
            this.actionMenu.add([button, text]);
            this.actionButtons.push({ button, text, action: actionData.action });
        });
        
        this.battleUI.add(this.actionMenu);
    }

    createBattleLog() {
        const { width, height } = this.cameras.main;
        
        this.logContainer = this.add.container(width/2, 50);
        this.logContainer.setDepth(600);
        
        // Fond du log
        const logBg = this.add.rectangle(0, 0, width - 40, 80, 0x2c3e50, 0.9);
        logBg.setStrokeStyle(1, 0x3498db);
        
        // Texte du log
        this.logText = this.add.text(0, 0, 'Le combat commence!', {
            fontSize: '14px',
            color: '#ffffff',
            align: 'center',
            wordWrap: { width: width - 60 }
        }).setOrigin(0.5);
        
        this.logContainer.add([logBg, this.logText]);
    }

    createCombatants() {
        const { width, height } = this.cameras.main;
        
        // Choisir le sprite selon le clan
        let playerSprite = 'player_sprite'; // fallback
        switch (gameState.player.clan) {
            case 'ryujin':
                playerSprite = 'samurai_sprite'; // Clan du feu = samurai
                break;
            case 'yurei':
                playerSprite = 'shinobi_sprite'; // Clan de l'ombre = shinobi
                break;
            case 'kaze':
                playerSprite = 'fighter_sprite'; // Clan du vent = fighter
                break;
        }

        // Sprite du joueur (gauche)
        this.playerSprite = this.add.sprite(width * 0.25, height * 0.4, playerSprite);
        this.playerSprite.setDisplaySize(64, 64);
        this.playerSprite.setDepth(100);
        
        // Appliquer la couleur du clan
        const clanData = GameConfig.clans[gameState.player.clan];
        if (clanData) {
            this.playerSprite.setTint(clanData.color);
        }
        
        // Sprite de l'ennemi (droite)
        this.enemySprite = this.add.sprite(width * 0.75, height * 0.4, 'enemy_sprite');
        this.enemySprite.setDisplaySize(64, 64);
        this.enemySprite.setDepth(100);
        this.enemySprite.setTint(0xff6b6b);
        
        // Animations d'idle
        this.createIdleAnimations();
    }

    createIdleAnimations() {
        // Animation d'idle pour le joueur
        this.tweens.add({
            targets: this.playerSprite,
            y: this.playerSprite.y - 5,
            duration: 1500,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
        
        // Animation d'idle pour l'ennemi
        this.tweens.add({
            targets: this.enemySprite,
            y: this.enemySprite.y - 3,
            duration: 2000,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
    }

    startTurn() {
        if (combatSystem.battleState === 'player_turn') {
            this.startPlayerTurn();
        } else if (combatSystem.battleState === 'enemy_turn') {
            this.startEnemyTurn();
        }
    }

    startPlayerTurn() {
        this.isPlayerTurn = true;
        this.actionSelected = false;
        
        // Activer les boutons d'action
        this.setActionButtonsEnabled(true);
        
        this.updateBattleLog("À votre tour! Choisissez une action.");
    }

    startEnemyTurn() {
        this.isPlayerTurn = false;
        
        // Désactiver les boutons d'action
        this.setActionButtonsEnabled(false);
        
        this.updateBattleLog("Tour de l'ennemi...");
        
        // Délai avant l'action de l'ennemi
        this.time.delayedCall(1500, () => {
            this.executeEnemyAction();
        });
    }

    selectAction(action) {
        if (!this.isPlayerTurn || this.actionSelected || this.isAnimating) {
            return;
        }
        
        this.actionSelected = true;
        this.setActionButtonsEnabled(false);
        
        console.log(`🎯 Action sélectionnée: ${action}`);
        
        switch (action) {
            case 'attack':
                this.executePlayerAttack();
                break;
            case 'jutsu':
                this.showJutsuMenu();
                break;
            case 'items':
                this.showItemsMenu();
                break;
            case 'flee':
                this.attemptFlee();
                break;
        }
    }

    executePlayerAttack() {
        const result = combatSystem.executePlayerAction('attack', { type: 'light' });

        if (result && result.success) {
            this.animateAttack(this.playerSprite, this.enemySprite, () => {
                this.updateBattleLog(result.message);
                this.updateHealthBars();

                // Vérifier si le combat est terminé
                if (this.checkBattleEnd()) {
                    return; // Combat terminé
                }

                // Passer au tour de l'ennemi
                this.time.delayedCall(1000, () => {
                    this.startEnemyTurn();
                });
            });
        } else {
            this.updateBattleLog(result?.message || "Attaque échouée!");
            this.actionSelected = false;
            this.setActionButtonsEnabled(true);
        }
    }

    showJutsuMenu() {
        // TODO: Implémenter le menu des jutsu
        this.updateBattleLog("Menu des jutsu à implémenter!");
        this.actionSelected = false;
        this.setActionButtonsEnabled(true);
    }

    showItemsMenu() {
        // TODO: Implémenter le menu des objets
        this.updateBattleLog("Menu des objets à implémenter!");
        this.actionSelected = false;
        this.setActionButtonsEnabled(true);
    }

    attemptFlee() {
        const result = combatSystem.executePlayerAction('flee');
        
        if (result && result.success) {
            this.updateBattleLog("Vous avez fui le combat!");
            this.endBattle();
        } else {
            this.updateBattleLog("Impossible de fuir!");
            this.actionSelected = false;
            this.setActionButtonsEnabled(true);
        }
    }

    executeEnemyAction() {
        // Exécuter l'action de l'ennemi via le CombatSystem
        const result = combatSystem.executeEnemyAction();

        if (result && result.success) {
            // Animer l'attaque de l'ennemi
            this.animateAttack(this.enemySprite, this.playerSprite, () => {
                this.updateBattleLog(result.message);
                this.updateHealthBars();

                // Vérifier si le combat continue
                if (this.checkBattleEnd()) {
                    return; // Combat terminé
                }

                // Retour au tour du joueur
                this.time.delayedCall(1000, () => {
                    this.startPlayerTurn();
                });
            });
        } else {
            // Erreur ou action échouée
            this.updateBattleLog(result?.message || "L'ennemi hésite...");

            // Retour au tour du joueur après un délai
            this.time.delayedCall(1500, () => {
                this.startPlayerTurn();
            });
        }
    }

    animateAttack(attacker, target, onComplete) {
        this.isAnimating = true;
        
        const originalX = attacker.x;
        
        // Animation d'attaque
        this.tweens.add({
            targets: attacker,
            x: originalX + (attacker === this.playerSprite ? 50 : -50),
            duration: 200,
            ease: 'Power2',
            yoyo: true,
            onComplete: () => {
                // Effet d'impact
                this.createImpactEffect(target.x, target.y);
                
                // Secousse de la cible
                this.tweens.add({
                    targets: target,
                    x: target.x + (Math.random() - 0.5) * 10,
                    y: target.y + (Math.random() - 0.5) * 10,
                    duration: 100,
                    yoyo: true,
                    repeat: 2,
                    onComplete: () => {
                        this.isAnimating = false;
                        if (onComplete) onComplete();
                    }
                });
            }
        });
    }

    createImpactEffect(x, y) {
        // Effet de particules d'impact
        for (let i = 0; i < 8; i++) {
            const particle = this.add.circle(x, y, 3, 0xffffff);
            
            this.tweens.add({
                targets: particle,
                x: x + Phaser.Math.Between(-30, 30),
                y: y + Phaser.Math.Between(-30, 30),
                alpha: 0,
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    particle.destroy();
                }
            });
        }
    }

    updateHealthBars() {
        const player = this.battleData.player;
        const enemy = this.battleData.enemy;
        
        // Barre HP du joueur
        const playerHPPercent = player.hp / player.maxHp;
        this.playerHPBar.width = 150 * playerHPPercent;
        this.playerHPText.setText(`${player.hp}/${player.maxHp}`);
        
        // Barre Chakra du joueur
        const playerChakraPercent = player.chakra / player.maxChakra;
        this.playerChakraBar.width = 150 * playerChakraPercent;
        this.playerChakraText.setText(`${player.chakra}/${player.maxChakra}`);
        
        // Barre HP de l'ennemi
        const enemyHPPercent = enemy.currentHp / enemy.maxHp;
        this.enemyHPBar.width = -150 * enemyHPPercent; // Négatif car origine à droite
        this.enemyHPText.setText(`${enemy.currentHp}/${enemy.maxHp}`);
    }

    checkBattleEnd() {
        const battleState = combatSystem.battleState;

        if (battleState === 'victory') {
            this.updateBattleLog("Victoire! L'ennemi est vaincu!");
            this.time.delayedCall(1500, () => {
                this.endBattle();
            });
            return true;
        } else if (battleState === 'defeat') {
            this.updateBattleLog("Vous avez été vaincu!");
            this.time.delayedCall(1500, () => {
                this.endBattle();
            });
            return true;
        }

        return false; // Combat continue
    }

    endBattle() {
        console.log('🏁 Fin du combat');
        
        // Nettoyer le système de combat
        combatSystem.cleanup();
        
        // Retourner à la scène d'exploration
        this.scene.resume('ExplorationScene');
        this.scene.stop();
    }

    updateBattleLog(message) {
        this.logText.setText(message);
        
        // Animation du texte
        this.logText.setAlpha(0);
        this.tweens.add({
            targets: this.logText,
            alpha: 1,
            duration: 300,
            ease: 'Power2'
        });
    }

    setActionButtonsEnabled(enabled) {
        this.actionButtons.forEach(buttonData => {
            buttonData.button.setInteractive(enabled);
            buttonData.button.setAlpha(enabled ? 0.8 : 0.4);
            buttonData.text.setAlpha(enabled ? 1 : 0.6);
        });
    }

    update(time, delta) {
        // Mettre à jour les barres de vie en temps réel
        this.updateHealthBars();
    }
}
