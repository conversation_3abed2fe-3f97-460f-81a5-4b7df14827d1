/**
 * Scène de village pour Shinobi Chronicles
 * Gère les interactions dans les villages (marchands, quêtes, repos)
 */

class VillageScene extends Phaser.Scene {
    constructor() {
        super({ key: 'VillageScene' });
        
        // Propriétés du village
        this.villageData = null;
        this.npcs = [];
        this.buildings = [];
        this.player = null;
        
        // Interface du village
        this.villageUI = null;
        this.currentInteraction = null;
        this.dialogBox = null;
        
        // Navigation
        this.cursors = null;
        this.isInDialog = false;
    }

    init(data) {
        // Recevoir les données du village
        this.villageData = data.village || this.getDefaultVillageData();
        console.log(`🏘️ Entrée dans le village: ${this.villageData.name}`);
    }

    create() {
        console.log('🏘️ Création du village simplifié...');

        // Nettoyage complet
        this.children.removeAll(true);
        this.tweens.killAll();
        this.time.removeAllEvents();

        // Créer un village ultra-simple
        this.createSimpleVillage();

        console.log('✅ Village simplifié créé');
    }

    createSimpleVillage() {
        const { width, height } = this.cameras.main;

        // Fond simple
        const bg = this.add.rectangle(width/2, height/2, width, height, 0x228B22);
        bg.setDepth(0);

        // Titre
        const title = this.add.text(width/2, 50, '🏘️ Village de Konoha', {
            fontSize: '28px',
            color: '#ffffff',
            backgroundColor: 'rgba(0,0,0,0.8)',
            padding: { x: 20, y: 10 }
        }).setOrigin(0.5);
        title.setDepth(1000);

        // Bouton de retour ÉNORME
        const backBtn = this.add.rectangle(100, 100, 200, 60, 0xe74c3c);
        backBtn.setStrokeStyle(3, 0xffffff);
        backBtn.setInteractive();
        backBtn.setDepth(1000);

        const backText = this.add.text(100, 100, '← RETOUR', {
            fontSize: '20px',
            color: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        backText.setDepth(1001);

        backBtn.on('pointerdown', () => {
            console.log('🚪 Retour à l\'exploration');
            this.scene.resume('ExplorationScene');
            this.scene.stop();
        });

        // Bâtiments simples avec boutons
        const buildings = [
            { name: '🔨 Forge', x: width*0.2, y: height*0.4, action: 'forge' },
            { name: '🏫 École', x: width*0.5, y: height*0.3, action: 'school' },
            { name: '⛩️ Temple', x: width*0.8, y: height*0.4, action: 'temple' },
            { name: '🏪 Boutique', x: width*0.3, y: height*0.7, action: 'shop' },
            { name: '🏠 Maison', x: width*0.7, y: height*0.7, action: 'home' }
        ];

        buildings.forEach(building => {
            // Rectangle du bâtiment
            const rect = this.add.rectangle(building.x, building.y, 120, 80, 0x8B4513);
            rect.setStrokeStyle(2, 0x000000);
            rect.setInteractive();
            rect.setDepth(10);

            // Texte du bâtiment
            const text = this.add.text(building.x, building.y, building.name, {
                fontSize: '14px',
                color: '#ffffff',
                backgroundColor: 'rgba(0,0,0,0.7)',
                padding: { x: 8, y: 4 }
            }).setOrigin(0.5);
            text.setDepth(20);

            // Interaction
            rect.on('pointerdown', () => {
                this.handleBuildingClick(building.action, building.name);
            });

            rect.on('pointerover', () => {
                rect.setFillStyle(0xA0522D);
            });

            rect.on('pointerout', () => {
                rect.setFillStyle(0x8B4513);
            });
        });

        // Instructions
        const instructions = this.add.text(width/2, height - 30,
            '💡 Cliquez sur les bâtiments pour entrer', {
            fontSize: '16px',
            color: '#ffffff',
            backgroundColor: 'rgba(0,0,0,0.8)',
            padding: { x: 15, y: 8 }
        }).setOrigin(0.5);
        instructions.setDepth(1000);

        // Désactiver complètement l'update pour éviter les erreurs
        this.updateEnabled = false;
    }

    handleBuildingClick(action, name) {
        console.log(`🏢 Clic sur ${name} (${action})`);

        // Afficher un message simple
        const { width, height } = this.cameras.main;

        const message = this.add.text(width/2, height/2,
            `Vous entrez dans ${name}\n\n(Fonctionnalité en développement)\n\nCliquez pour fermer`, {
            fontSize: '18px',
            color: '#ffffff',
            backgroundColor: 'rgba(0,0,0,0.9)',
            padding: { x: 30, y: 20 },
            align: 'center'
        }).setOrigin(0.5);
        message.setDepth(2000);
        message.setInteractive();

        message.on('pointerdown', () => {
            message.destroy();
        });

        // Auto-fermeture après 3 secondes
        this.time.delayedCall(3000, () => {
            if (message && message.active) {
                message.destroy();
            }
        });
    }

    getDefaultVillageData() {
        return {
            name: "Village de Konoha",
            type: "main_village",
            npcs: [
                { id: "sensei", type: "trainer", name: "Maître Iruka", x: 5, y: 3 },
                { id: "merchant", type: "shop", name: "Marchand Tenten", x: 8, y: 5 },
                { id: "innkeeper", type: "inn", name: "Aubergiste", x: 3, y: 7 },
                { id: "mission_board", type: "quests", name: "Tableau des Missions", x: 6, y: 4 }
            ],
            buildings: [
                { id: "academy", type: "training", name: "Académie Ninja", x: 4, y: 2, width: 3, height: 2 },
                { id: "shop", type: "merchant", name: "Magasin d'Armes", x: 7, y: 4, width: 2, height: 2 },
                { id: "inn", type: "rest", name: "Auberge", x: 2, y: 6, width: 2, height: 3 },
                { id: "shrine", type: "chakra", name: "Sanctuaire", x: 9, y: 2, width: 2, height: 2 }
            ]
        };
    }

    createVillageEnvironment() {
        const { width, height } = this.cameras.main;
        
        // Arrière-plan du village
        const bg = this.add.graphics();
        bg.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x98FB98, 0x98FB98);
        bg.fillRect(0, 0, width, height);
        
        // Créer une grille de village (12x10)
        this.villageGrid = [];
        this.tileSize = 64;
        this.villageWidth = 12;
        this.villageHeight = 10;
        
        for (let y = 0; y < this.villageHeight; y++) {
            this.villageGrid[y] = [];
            for (let x = 0; x < this.villageWidth; x++) {
                this.villageGrid[y][x] = this.getVillageTileType(x, y);
                this.createVillageTile(x, y, this.villageGrid[y][x]);
            }
        }
        
        // Ajouter des décorations
        this.addVillageDecorations();
    }

    getVillageTileType(x, y) {
        // Chemins principaux
        if (x === 6 || y === 5) {
            return 'path';
        }
        
        // Bordures
        if (x === 0 || x === this.villageWidth - 1 || y === 0 || y === this.villageHeight - 1) {
            return 'fence';
        }
        
        // Zones d'herbe par défaut
        return 'grass';
    }

    createVillageTile(x, y, tileType) {
        const worldX = x * this.tileSize + this.tileSize/2;
        const worldY = y * this.tileSize + this.tileSize/2;
        
        let color = 0x98FB98; // Vert herbe par défaut
        
        switch (tileType) {
            case 'path':
                color = 0xDEB887; // Beige pour les chemins
                break;
            case 'fence':
                color = 0x8B4513; // Marron pour les clôtures
                break;
            case 'water':
                color = 0x87CEEB; // Bleu pour l'eau
                break;
        }
        
        const tile = this.add.rectangle(worldX, worldY, this.tileSize, this.tileSize, color);
        tile.setStrokeStyle(1, 0x000000, 0.1);
        tile.setDepth(0);
    }

    addVillageDecorations() {
        // Ajouter quelques arbres décoratifs
        const treePositions = [
            { x: 1, y: 1 }, { x: 10, y: 1 }, { x: 1, y: 8 }, { x: 10, y: 8 }
        ];
        
        treePositions.forEach(pos => {
            const tree = this.add.circle(
                pos.x * this.tileSize + this.tileSize/2,
                pos.y * this.tileSize + this.tileSize/2,
                this.tileSize/3,
                0x228B22
            );
            tree.setDepth(10);
        });
        
        // Fontaine centrale
        const fountain = this.add.circle(
            6 * this.tileSize + this.tileSize/2,
            5 * this.tileSize + this.tileSize/2,
            this.tileSize/4,
            0x87CEEB
        );
        fountain.setDepth(5);
        fountain.setStrokeStyle(3, 0x4682B4);
    }

    createPlayer() {
        // Position de départ du joueur (entrée du village)
        const startX = 6 * this.tileSize + this.tileSize/2;
        const startY = 9 * this.tileSize + this.tileSize/2;
        
        this.player = this.physics.add.sprite(startX, startY, 'player_sprite');
        this.player.setDisplaySize(this.tileSize * 0.6, this.tileSize * 0.6);
        this.player.setDepth(100);
        
        // Appliquer la couleur du clan
        const clanData = GameConfig.clans[gameState.player.clan];
        if (clanData) {
            this.player.setTint(clanData.color);
        }
        
        // Configuration physique
        this.player.setCollideWorldBounds(true);
        this.player.body.setSize(this.tileSize * 0.4, this.tileSize * 0.4);
    }

    createNPCs() {
        this.npcs = [];
        
        this.villageData.npcs.forEach(npcData => {
            const npc = this.createNPC(npcData);
            this.npcs.push(npc);
        });
    }

    createNPC(npcData) {
        const worldX = npcData.x * this.tileSize + this.tileSize/2;
        const worldY = npcData.y * this.tileSize + this.tileSize/2;
        
        // Créer le sprite NPC
        const npc = this.physics.add.sprite(worldX, worldY, 'enemy_sprite');
        npc.setDisplaySize(this.tileSize * 0.7, this.tileSize * 0.7);
        npc.setDepth(50);
        
        // Couleur selon le type
        const npcColors = {
            trainer: 0x3498db,
            shop: 0xf39c12,
            inn: 0x27ae60,
            quests: 0x9b59b6
        };
        
        npc.setTint(npcColors[npcData.type] || 0xffffff);
        
        // Données du NPC
        npc.npcData = npcData;
        npc.setImmovable(true);
        
        // Zone d'interaction
        const interactionZone = this.add.circle(worldX, worldY, this.tileSize, 0x00ff00, 0);
        interactionZone.setDepth(1);
        interactionZone.npcData = npcData;
        
        // Collision avec le joueur
        this.physics.add.overlap(this.player, npc, () => {
            this.showInteractionPrompt(npcData);
        });
        
        // Animation d'idle
        this.tweens.add({
            targets: npc,
            y: npc.y - 3,
            duration: 2000,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
        
        // Nom au-dessus du NPC
        const nameText = this.add.text(worldX, worldY - this.tileSize/2, npcData.name, {
            fontSize: '12px',
            color: '#ffffff',
            backgroundColor: '#000000',
            padding: { x: 4, y: 2 }
        }).setOrigin(0.5);
        nameText.setDepth(200);
        
        return { sprite: npc, nameText: nameText, data: npcData };
    }

    createBuildings() {
        this.buildings = [];
        
        this.villageData.buildings.forEach(buildingData => {
            const building = this.createBuilding(buildingData);
            this.buildings.push(building);
        });
    }

    createBuilding(buildingData) {
        const worldX = buildingData.x * this.tileSize;
        const worldY = buildingData.y * this.tileSize;
        const width = buildingData.width * this.tileSize;
        const height = buildingData.height * this.tileSize;
        
        // Couleurs selon le type de bâtiment
        const buildingColors = {
            training: 0x3498db,
            merchant: 0xf39c12,
            rest: 0x27ae60,
            chakra: 0x9b59b6
        };
        
        const color = buildingColors[buildingData.type] || 0x95a5a6;
        
        // Créer le bâtiment
        const building = this.add.rectangle(
            worldX + width/2,
            worldY + height/2,
            width,
            height,
            color,
            0.8
        );
        building.setStrokeStyle(3, 0x2c3e50);
        building.setDepth(20);
        
        // Nom du bâtiment
        const nameText = this.add.text(
            worldX + width/2,
            worldY + height/2,
            buildingData.name,
            {
                fontSize: '10px',
                color: '#ffffff',
                align: 'center',
                wordWrap: { width: width - 10 }
            }
        ).setOrigin(0.5);
        nameText.setDepth(25);
        
        return { sprite: building, nameText: nameText, data: buildingData };
    }

    setupControls() {
        this.cursors = this.input.keyboard.createCursorKeys();
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');
        this.actionKeys = this.input.keyboard.addKeys('SPACE,E,ESC');
        
        this.input.keyboard.on('keydown', this.handleKeyDown, this);
    }

    setupCamera() {
        // Centrer la caméra sur le village
        const villagePixelWidth = this.villageWidth * this.tileSize;
        const villagePixelHeight = this.villageHeight * this.tileSize;
        
        this.cameras.main.setBounds(0, 0, villagePixelWidth, villagePixelHeight);
        this.cameras.main.startFollow(this.player);
        this.cameras.main.setZoom(1.2);
    }

    createVillageUI() {
        const { width, height } = this.cameras.main;
        
        // Panel d'information en haut
        this.villageUI = this.add.container(0, 0);
        this.villageUI.setDepth(1000);
        this.villageUI.setScrollFactor(0);
        
        // Nom du village
        const villageNameBg = this.add.rectangle(width/2, 30, 300, 40, 0x2c3e50, 0.9);
        villageNameBg.setStrokeStyle(2, 0x3498db);
        
        const villageName = this.add.text(width/2, 30, this.villageData.name, {
            fontSize: '18px',
            color: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        
        this.villageUI.add([villageNameBg, villageName]);
        
        // Bouton de sortie
        this.createExitButton();
    }

    createExitButton() {
        const { width, height } = this.cameras.main;

        // Bouton de sortie amélioré en haut à gauche
        const exitContainer = this.add.container(80, 80);
        exitContainer.setScrollFactor(0);
        exitContainer.setDepth(1001);

        const exitButton = this.add.rectangle(0, 0, 160, 45, 0xe74c3c, 0.9);
        exitButton.setStrokeStyle(2, 0xffffff);
        exitButton.setInteractive();

        const exitText = this.add.text(0, 0, '← Retour Exploration', {
            fontSize: '14px',
            color: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        exitContainer.add([exitButton, exitText]);
        exitContainer.setSize(160, 45);
        exitContainer.setInteractive();

        exitContainer.on('pointerover', () => {
            exitButton.setFillStyle(0xc0392b);
            this.tweens.add({
                targets: exitContainer,
                scaleX: 1.05,
                scaleY: 1.05,
                duration: 100
            });
        });

        exitContainer.on('pointerout', () => {
            exitButton.setFillStyle(0xe74c3c);
            this.tweens.add({
                targets: exitContainer,
                scaleX: 1,
                scaleY: 1,
                duration: 100
            });
        });

        exitContainer.on('pointerdown', () => {
            this.exitVillage();
        });

        // Instructions en bas
        const instructions = this.add.text(width/2, height - 20,
            '💡 Survolez les bâtiments • Cliquez pour entrer • ESPACE pour interagir avec les NPCs', {
            fontSize: '12px',
            color: '#ffffff',
            backgroundColor: 'rgba(0,0,0,0.8)',
            padding: { x: 10, y: 5 }
        }).setOrigin(0.5, 1);
        instructions.setScrollFactor(0);
        instructions.setDepth(1000);

        this.villageUI.add([exitContainer, instructions]);
    }

    showInteractionPrompt(npcData) {
        if (this.isInDialog) return;
        
        // Afficher un prompt d'interaction
        if (!this.interactionPrompt) {
            this.interactionPrompt = this.add.text(
                this.player.x,
                this.player.y - 40,
                'Appuyez sur ESPACE pour interagir',
                {
                    fontSize: '12px',
                    color: '#ffffff',
                    backgroundColor: '#000000',
                    padding: { x: 8, y: 4 }
                }
            ).setOrigin(0.5);
            this.interactionPrompt.setDepth(300);
        }
        
        this.currentInteraction = npcData;
    }

    hideInteractionPrompt() {
        if (this.interactionPrompt) {
            this.interactionPrompt.destroy();
            this.interactionPrompt = null;
        }
        this.currentInteraction = null;
    }

    handleKeyDown(event) {
        switch (event.code) {
            case 'Space':
            case 'KeyE':
                if (this.currentInteraction) {
                    this.interactWithNPC(this.currentInteraction);
                }
                break;
            case 'Escape':
                if (this.isInDialog) {
                    this.closeDialog();
                } else {
                    this.exitVillage();
                }
                break;
        }
    }

    interactWithNPC(npcData) {
        console.log(`💬 Interaction avec ${npcData.name}`);
        
        this.isInDialog = true;
        this.hideInteractionPrompt();
        
        switch (npcData.type) {
            case 'trainer':
                this.openTrainerDialog(npcData);
                break;
            case 'shop':
                this.openShopDialog(npcData);
                break;
            case 'inn':
                this.openInnDialog(npcData);
                break;
            case 'quests':
                this.openQuestDialog(npcData);
                break;
            default:
                this.openGenericDialog(npcData);
        }
    }

    openTrainerDialog(npcData) {
        this.showDialog(npcData.name, [
            "Bienvenue à l'académie ninja!",
            "Ici vous pouvez améliorer vos compétences.",
            "Voulez-vous vous entraîner?"
        ], [
            { text: "S'entraîner", action: () => this.startTraining() },
            { text: "Plus tard", action: () => this.closeDialog() }
        ]);
    }

    openShopDialog(npcData) {
        this.showDialog(npcData.name, [
            "Bienvenue dans ma boutique!",
            "J'ai les meilleures armes ninja.",
            "Que puis-je faire pour vous?"
        ], [
            { text: "Acheter", action: () => this.openShop() },
            { text: "Vendre", action: () => this.openSellMenu() },
            { text: "Partir", action: () => this.closeDialog() }
        ]);
    }

    openInnDialog(npcData) {
        this.showDialog(npcData.name, [
            "Vous semblez fatigué, ninja.",
            "Une nuit de repos vous ferait du bien.",
            `Cela coûte 50 Ryo. Vous avez ${gameState.inventory.ryo} Ryo.`
        ], [
            { text: "Se reposer (50 Ryo)", action: () => this.restAtInn() },
            { text: "Non merci", action: () => this.closeDialog() }
        ]);
    }

    showDialog(speakerName, messages, options) {
        const { width, height } = this.cameras.main;
        
        // Créer la boîte de dialogue
        this.dialogBox = this.add.container(width/2, height - 150);
        this.dialogBox.setDepth(2000);
        this.dialogBox.setScrollFactor(0);
        
        // Fond de la boîte
        const dialogBg = this.add.rectangle(0, 0, width - 40, 120, 0x2c3e50, 0.95);
        dialogBg.setStrokeStyle(3, 0x3498db);
        
        // Nom du speaker
        const speakerNameText = this.add.text(-width/2 + 30, -40, speakerName, {
            fontSize: '16px',
            color: '#feca57',
            fontWeight: 'bold'
        });
        
        // Messages
        const messageText = this.add.text(-width/2 + 30, -10, messages.join('\n'), {
            fontSize: '14px',
            color: '#ffffff',
            wordWrap: { width: width - 100 }
        });
        
        this.dialogBox.add([dialogBg, speakerNameText, messageText]);
        
        // Boutons d'options
        if (options && options.length > 0) {
            this.createDialogOptions(options);
        }
    }

    createDialogOptions(options) {
        const { width } = this.cameras.main;
        
        options.forEach((option, index) => {
            const x = -width/2 + 30 + (index * 120);
            const y = 35;
            
            const button = this.add.rectangle(x, y, 100, 25, 0x3498db, 0.8);
            button.setStrokeStyle(1, 0xffffff);
            button.setInteractive();
            
            const buttonText = this.add.text(x, y, option.text, {
                fontSize: '12px',
                color: '#ffffff'
            }).setOrigin(0.5);
            
            button.on('pointerover', () => {
                button.setAlpha(1);
                button.setScale(1.05);
            });
            
            button.on('pointerout', () => {
                button.setAlpha(0.8);
                button.setScale(1);
            });
            
            button.on('pointerdown', () => {
                if (option.action) {
                    option.action();
                }
            });
            
            this.dialogBox.add([button, buttonText]);
        });
    }

    closeDialog() {
        if (this.dialogBox) {
            this.dialogBox.destroy();
            this.dialogBox = null;
        }
        this.isInDialog = false;
    }

    // Actions des NPCs
    startTraining() {
        console.log('🥋 Début de l\'entraînement');
        this.closeDialog();
        // TODO: Implémenter le système d'entraînement
    }

    openShop() {
        console.log('🛒 Ouverture de la boutique');
        this.closeDialog();
        // TODO: Implémenter l'interface de boutique
    }

    restAtInn() {
        if (gameState.inventory.ryo >= 50) {
            gameState.inventory.ryo -= 50;
            gameState.player.hp = gameState.player.maxHp;
            gameState.player.chakra = gameState.player.maxChakra;
            
            this.showDialog("Aubergiste", [
                "Vous vous sentez complètement reposé!",
                "Vos HP et Chakra ont été restaurés."
            ], [
                { text: "Merci", action: () => this.closeDialog() }
            ]);
        } else {
            this.showDialog("Aubergiste", [
                "Désolé, vous n'avez pas assez d'argent."
            ], [
                { text: "D'accord", action: () => this.closeDialog() }
            ]);
        }
    }

    exitVillage() {
        console.log('🚪 Sortie du village');
        
        // Retourner à la scène d'exploration
        this.scene.resume('ExplorationScene');
        this.scene.stop();
    }

    playVillageMusic() {
        // TODO: Jouer la musique du village
        console.log('🎵 Musique du village');
    }

    update(time, delta) {
        // Update désactivé pour le village simplifié
        if (!this.updateEnabled) {
            return;
        }

        // Ancien code commenté pour éviter les erreurs
        // this.updatePlayerMovement();
        // this.checkInteractions();
    }

    updatePlayerMovement() {
        if (this.isInDialog) return;
        
        const speed = 150;
        this.player.body.setVelocity(0);
        
        if (this.cursors.left.isDown || this.wasd.A.isDown) {
            this.player.body.setVelocityX(-speed);
        } else if (this.cursors.right.isDown || this.wasd.D.isDown) {
            this.player.body.setVelocityX(speed);
        }
        
        if (this.cursors.up.isDown || this.wasd.W.isDown) {
            this.player.body.setVelocityY(-speed);
        } else if (this.cursors.down.isDown || this.wasd.S.isDown) {
            this.player.body.setVelocityY(speed);
        }
    }

    checkInteractions() {
        // Vérifier si le joueur est près d'un NPC
        let nearNPC = false;
        
        this.npcs.forEach(npc => {
            const distance = Phaser.Math.Distance.Between(
                this.player.x, this.player.y,
                npc.sprite.x, npc.sprite.y
            );
            
            if (distance < this.tileSize) {
                this.showInteractionPrompt(npc.data);
                nearNPC = true;
            }
        });
        
        if (!nearNPC) {
            this.hideInteractionPrompt();
        }
    }

    // === SYSTÈME DE BÂTIMENTS CLIQUABLES ===

    loadVillageImage() {
        // Vérifier si une image de village existe
        if (this.textures.exists('village_map')) {
            const { width, height } = this.cameras.main;
            this.villageImage = this.add.image(width/2, height/2, 'village_map');
            this.villageImage.setDisplaySize(width, height);
            this.villageImage.setDepth(0);

            // Créer les zones cliquables des bâtiments
            this.createBuildingClickZones();
        } else {
            console.log('⚠️ Image village_map non trouvée, création d\'un fond de remplacement');
            this.createFallbackVillage();
        }
    }

    createFallbackVillage() {
        const { width, height } = this.cameras.main;

        // Créer un fond simple
        const bg = this.add.rectangle(width/2, height/2, width, height, 0x228B22);
        bg.setDepth(0);

        // Ajouter quelques bâtiments simples
        const buildings = [
            { x: width*0.2, y: height*0.3, w: 80, h: 60, color: 0x8B4513, name: "Forge" },
            { x: width*0.5, y: height*0.2, w: 100, h: 70, color: 0x4169E1, name: "École" },
            { x: width*0.8, y: height*0.4, w: 90, h: 80, color: 0xFFD700, name: "Temple" },
            { x: width*0.6, y: height*0.6, w: 80, h: 60, color: 0xFF6347, name: "Boutique" },
            { x: width*0.3, y: height*0.7, w: 70, h: 50, color: 0x32CD32, name: "Maison" }
        ];

        buildings.forEach(b => {
            const building = this.add.rectangle(b.x, b.y, b.w, b.h, b.color);
            building.setStrokeStyle(2, 0x000000);
            building.setDepth(10);

            const label = this.add.text(b.x, b.y, b.name, {
                fontSize: '12px',
                color: '#ffffff',
                backgroundColor: '#000000',
                padding: { x: 4, y: 2 }
            }).setOrigin(0.5);
            label.setDepth(20);
        });
    }

    createBuildingClickZones() {
        // Définition des bâtiments selon votre découpage
        const buildings = [
            { name: "Forge", x: 110, y: 120, width: 80, height: 60, type: "forge" },
            { name: "École de Ninjutsu", x: 250, y: 110, width: 100, height: 70, type: "school" },
            { name: "Temple", x: 400, y: 130, width: 90, height: 80, type: "temple" },
            { name: "Boutique", x: 260, y: 250, width: 80, height: 60, type: "shop" },
            { name: "Maison du Joueur", x: 140, y: 270, width: 70, height: 50, type: "home" },
            { name: "Escalier vers Donjon", x: 370, y: 10, width: 60, height: 40, type: "dungeon" }
        ];

        buildings.forEach(building => {
            this.createClickableBuilding(building);
        });
    }

    createClickableBuilding(buildingData) {
        // Créer une zone invisible cliquable
        const clickZone = this.add.rectangle(
            buildingData.x + buildingData.width/2,
            buildingData.y + buildingData.height/2,
            buildingData.width,
            buildingData.height,
            0x00ff00,
            0 // Transparent
        );

        clickZone.setInteractive();
        clickZone.setDepth(100);

        // Effet de survol
        clickZone.on('pointerover', () => {
            clickZone.setAlpha(0.3); // Légèrement visible au survol
            this.showBuildingTooltip(buildingData);
        });

        clickZone.on('pointerout', () => {
            clickZone.setAlpha(0); // Redevient invisible
            this.hideBuildingTooltip();
        });

        // Clic sur le bâtiment
        clickZone.on('pointerdown', () => {
            this.enterBuilding(buildingData);
        });

        return clickZone;
    }

    showBuildingTooltip(buildingData) {
        if (this.buildingTooltip) {
            this.buildingTooltip.destroy();
        }

        const { width, height } = this.cameras.main;

        // Position de base
        let tooltipX = buildingData.x + buildingData.width/2;
        let tooltipY = buildingData.y - 15;

        // Ajustements pour éviter les débordements
        const tooltipWidth = buildingData.name.length * 8 + 20;

        if (tooltipX + tooltipWidth/2 > width) {
            tooltipX = width - tooltipWidth/2 - 10;
        }
        if (tooltipX - tooltipWidth/2 < 0) {
            tooltipX = tooltipWidth/2 + 10;
        }
        if (tooltipY < 30) {
            tooltipY = buildingData.y + buildingData.height + 15;
        }

        this.buildingTooltip = this.add.text(
            tooltipX,
            tooltipY,
            buildingData.name,
            {
                fontSize: '14px',
                color: '#ffffff',
                backgroundColor: 'rgba(0,0,0,0.9)',
                padding: { x: 10, y: 6 },
                stroke: '#3498db',
                strokeThickness: 1
            }
        ).setOrigin(0.5, 1);
        this.buildingTooltip.setDepth(1000);

        // Animation d'apparition
        this.buildingTooltip.setAlpha(0);
        this.tweens.add({
            targets: this.buildingTooltip,
            alpha: 1,
            duration: 200,
            ease: 'Power2'
        });
    }

    hideBuildingTooltip() {
        if (this.buildingTooltip) {
            this.buildingTooltip.destroy();
            this.buildingTooltip = null;
        }
    }

    enterBuilding(buildingData) {
        console.log(`🏢 Entrée dans: ${buildingData.name}`);

        switch (buildingData.type) {
            case 'forge':
                this.openForge();
                break;
            case 'school':
                this.openNinjutsuSchool();
                break;
            case 'temple':
                this.openTemple();
                break;
            case 'shop':
                this.openShop();
                break;
            case 'home':
                this.openPlayerHome();
                break;
            case 'dungeon':
                this.enterDungeon();
                break;
        }
    }

    // === FONCTIONS DES BÂTIMENTS ===

    openForge() {
        this.showDialog("Forgeron", [
            "Bienvenue à la forge!",
            "Je peux améliorer vos armes et armures.",
            "Que voulez-vous faire?"
        ], [
            { text: "Améliorer équipement", action: () => this.upgradeEquipment() },
            { text: "Réparer équipement", action: () => this.repairEquipment() },
            { text: "Partir", action: () => this.closeDialog() }
        ]);
    }

    openNinjutsuSchool() {
        this.showDialog("Maître Ninja", [
            "Bienvenue à l'école de ninjutsu!",
            "Ici vous pouvez apprendre de nouveaux jutsu.",
            `Vous avez ${gameState.player.skillPoints} points de compétence.`
        ], [
            { text: "Apprendre jutsu", action: () => this.learnJutsu() },
            { text: "Voir mes jutsu", action: () => this.viewJutsu() },
            { text: "Partir", action: () => this.closeDialog() }
        ]);
    }

    openTemple() {
        this.showDialog("Moine", [
            "Que la paix soit avec vous, ninja.",
            "Ce temple peut purifier votre chakra.",
            "Voulez-vous méditer?"
        ], [
            { text: "Méditer (Restaurer chakra)", action: () => this.meditate() },
            { text: "Bénédiction (100 Ryo)", action: () => this.getBlessing() },
            { text: "Partir", action: () => this.closeDialog() }
        ]);
    }

    openPlayerHome() {
        this.showDialog("Votre Maison", [
            "Bienvenue chez vous!",
            "Ici vous pouvez vous reposer et gérer vos affaires.",
            "Que voulez-vous faire?"
        ], [
            { text: "Se reposer", action: () => this.restAtHome() },
            { text: "Coffre", action: () => this.openChest() },
            { text: "Sortir", action: () => this.closeDialog() }
        ]);
    }

    enterDungeon() {
        this.showDialog("Escalier Mystérieux", [
            "Un escalier descend vers les profondeurs...",
            "Vous sentez une aura dangereuse.",
            "Voulez-vous descendre?"
        ], [
            { text: "Entrer dans le donjon", action: () => this.startDungeon() },
            { text: "Rebrousser chemin", action: () => this.closeDialog() }
        ]);
    }

    // === ACTIONS DES BÂTIMENTS ===

    upgradeEquipment() {
        this.closeDialog();
        console.log("🔨 Système d'amélioration d'équipement à implémenter");
    }

    learnJutsu() {
        this.closeDialog();
        console.log("📚 Système d'apprentissage de jutsu à implémenter");
    }

    meditate() {
        gameState.player.chakra = gameState.player.maxChakra;
        this.showDialog("Moine", [
            "Votre chakra a été entièrement restauré.",
            "Que la force soit avec vous."
        ], [
            { text: "Merci", action: () => this.closeDialog() }
        ]);
    }

    restAtHome() {
        gameState.player.hp = gameState.player.maxHp;
        gameState.player.chakra = gameState.player.maxChakra;
        this.showDialog("Votre Maison", [
            "Vous vous reposez dans votre lit.",
            "HP et Chakra entièrement restaurés!"
        ], [
            { text: "Parfait", action: () => this.closeDialog() }
        ]);
    }

    startDungeon() {
        this.closeDialog();
        console.log("🏰 Lancement du donjon à implémenter");
        // TODO: Lancer la scène de donjon
    }
}
