/**
 * Point d'entrée principal pour Shinobi Chronicles
 * Initialise le jeu et gère les transitions entre les écrans
 */

class ShinobiChronicles {
    constructor() {
        this.currentScreen = 'loading-screen';
        this.game = null;
        this.selectedClan = null;
        this.isGameInitialized = false;
        
        this.init();
    }

    // Initialisation du jeu
    init() {
        console.log('🥷 Initialisation de Shinobi Chronicles...');
        
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    // Quand le DOM est prêt
    onDOMReady() {
        this.setupEventListeners();
        this.startLoadingSequence();
    }

    // Configuration des écouteurs d'événements
    setupEventListeners() {
        // Boutons du menu principal
        const newGameBtn = document.getElementById('new-game-btn');
        const loadGameBtn = document.getElementById('load-game-btn');
        const settingsBtn = document.getElementById('settings-btn');

        if (newGameBtn) {
            newGameBtn.addEventListener('click', () => this.startNewGame());
        }
        
        if (loadGameBtn) {
            loadGameBtn.addEventListener('click', () => this.loadGame());
        }
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.openSettings());
        }

        // Sélection de clan
        const clanOptions = document.querySelectorAll('.clan-option');
        clanOptions.forEach(option => {
            option.addEventListener('click', (e) => this.selectClan(e.target.closest('.clan-option')));
        });

        // Bouton de démarrage du jeu
        const startGameBtn = document.getElementById('start-game-btn');
        if (startGameBtn) {
            startGameBtn.addEventListener('click', () => this.createCharacterAndStart());
        }

        // Boutons de l'interface de jeu
        this.setupGameUIListeners();

        // Gestion des raccourcis clavier
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));

        // Gestion de la fermeture de la page
        window.addEventListener('beforeunload', (e) => this.onBeforeUnload(e));
    }

    // Configuration des écouteurs pour l'interface de jeu
    setupGameUIListeners() {
        // Boutons rapides
        const inventoryBtn = document.getElementById('inventory-btn');
        const skillsBtn = document.getElementById('skills-btn');
        const mapBtn = document.getElementById('map-btn');
        const menuBtn = document.getElementById('menu-btn');

        if (inventoryBtn) {
            inventoryBtn.addEventListener('click', () => this.toggleInventory());
        }
        
        if (skillsBtn) {
            skillsBtn.addEventListener('click', () => this.toggleSkills());
        }
        
        if (mapBtn) {
            mapBtn.addEventListener('click', () => this.toggleMap());
        }
        
        if (menuBtn) {
            menuBtn.addEventListener('click', () => this.toggleGameMenu());
        }

        // Contrôles de mouvement mobile
        const moveButtons = document.querySelectorAll('.move-btn');
        moveButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const direction = e.target.dataset.direction;
                this.handleMovement(direction);
            });
        });

        // Boutons d'action
        const interactBtn = document.getElementById('interact-btn');
        const scanBtn = document.getElementById('scan-btn');

        if (interactBtn) {
            interactBtn.addEventListener('click', () => this.handleInteraction());
        }
        
        if (scanBtn) {
            scanBtn.addEventListener('click', () => this.handleScan());
        }
    }

    // Séquence de chargement
    startLoadingSequence() {
        this.showScreen('loading-screen');
        
        // Simuler le chargement des assets
        let progress = 0;
        const progressBar = document.querySelector('.loading-progress');
        
        const loadingInterval = setInterval(() => {
            progress += Math.random() * 15;
            
            if (progressBar) {
                progressBar.style.width = `${Math.min(progress, 100)}%`;
            }
            
            if (progress >= 100) {
                clearInterval(loadingInterval);
                setTimeout(() => {
                    this.finishLoading();
                }, 500);
            }
        }, 200);
    }

    // Fin du chargement
    finishLoading() {
        console.log('✅ Chargement terminé');
        
        // Vérifier s'il y a une sauvegarde existante
        const hasSave = this.checkForExistingSave();
        
        if (hasSave) {
            this.showScreen('main-menu');
        } else {
            // Pas de sauvegarde, aller directement à la création de personnage
            this.showScreen('character-creation');
        }
    }

    // Vérifier les sauvegardes existantes
    checkForExistingSave() {
        try {
            const saveData = localStorage.getItem('shinobi_save_1');
            return saveData !== null;
        } catch (error) {
            console.error('Erreur lors de la vérification des sauvegardes:', error);
            return false;
        }
    }

    // Afficher un écran spécifique
    showScreen(screenId) {
        // Cacher tous les écrans
        const screens = document.querySelectorAll('.screen');
        screens.forEach(screen => {
            screen.classList.remove('active');
        });

        // Afficher l'écran demandé
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.add('active');
            this.currentScreen = screenId;
            console.log(`📺 Écran affiché: ${screenId}`);
        }
    }

    // Démarrer une nouvelle partie
    startNewGame() {
        console.log('🆕 Nouvelle partie');
        this.showScreen('character-creation');
    }

    // Charger une partie existante
    loadGame() {
        console.log('📁 Chargement de partie');
        
        if (saveSystem.loadGame(1)) {
            this.initializeGame();
            this.showScreen('game-canvas');
        } else {
            this.showNotification('Aucune sauvegarde trouvée!', 'error');
        }
    }

    // Ouvrir les paramètres
    openSettings() {
        console.log('⚙️ Paramètres');
        // TODO: Implémenter l'écran des paramètres
        this.showNotification('Paramètres à venir!', 'info');
    }

    // Sélectionner un clan
    selectClan(clanElement) {
        // Retirer la sélection précédente
        document.querySelectorAll('.clan-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Sélectionner le nouveau clan
        clanElement.classList.add('selected');
        this.selectedClan = clanElement.dataset.clan;

        // Activer le bouton de démarrage
        const startBtn = document.getElementById('start-game-btn');
        if (startBtn) {
            startBtn.disabled = false;
        }

        console.log(`🏮 Clan sélectionné: ${this.selectedClan}`);
    }

    // Créer le personnage et démarrer le jeu
    createCharacterAndStart() {
        if (!this.selectedClan) {
            this.showNotification('Veuillez sélectionner un clan!', 'warning');
            return;
        }

        console.log('👤 Création du personnage...');

        // Initialiser le gameState avec le clan sélectionné
        gameState.state.player.clan = this.selectedClan;
        gameState.state.player.name = `Ninja ${this.selectedClan}`;

        // Appliquer les bonus de clan
        this.applyClanBonuses();

        // Sauvegarder l'état initial
        gameState.autoSave();

        // Initialiser et démarrer le jeu
        this.initializeGame();
        this.showScreen('game-canvas');
    }

    // Appliquer les bonus de clan
    applyClanBonuses() {
        const clanData = GameConfig.clans[this.selectedClan];
        if (!clanData) return;

        const player = gameState.state.player;
        
        // Appliquer les bonus de stats
        if (clanData.bonuses) {
            Object.keys(clanData.bonuses).forEach(stat => {
                if (player.stats[stat] !== undefined) {
                    player.stats[stat] += clanData.bonuses[stat];
                }
            });
        }

        // Ajouter le jutsu de départ
        if (clanData.startingJutsu && !player.jutsu.includes(clanData.startingJutsu)) {
            player.jutsu.push(clanData.startingJutsu);
        }

        console.log(`✨ Bonus de clan ${clanData.name} appliqués`);
    }

    // Initialiser le jeu Phaser
    initializeGame() {
        if (this.isGameInitialized) {
            console.log('🎮 Jeu déjà initialisé');
            return;
        }

        console.log('🎮 Initialisation du moteur de jeu...');

        try {
            // Créer l'instance Phaser
            this.game = new Phaser.Game(GameConfig.phaser);
            this.isGameInitialized = true;

            // Initialiser les systèmes
            this.initializeSystems();

            console.log('✅ Jeu initialisé avec succès');
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation du jeu:', error);
            this.showNotification('Erreur lors du démarrage du jeu!', 'error');
        }
    }

    // Initialiser les systèmes du jeu
    initializeSystems() {
        // Les systèmes sont déjà initialisés globalement
        // Ici on peut faire des configurations supplémentaires
        
        // Démarrer la régénération de chakra
        chakraSystem.initializeRegeneration();
        
        // Mettre à jour l'interface
        this.updateUI();
        
        console.log('🔧 Systèmes initialisés');
    }

    // Mettre à jour l'interface utilisateur
    updateUI() {
        // Mettre à jour les barres de stats
        this.updateStatsDisplay();
        
        // Mettre à jour la mini-carte
        this.updateMiniMap();
    }

    // Mettre à jour l'affichage des stats
    updateStatsDisplay() {
        const player = gameState.player;
        
        // HP
        const hpFill = document.getElementById('hp-fill');
        const hpText = document.getElementById('hp-text');
        if (hpFill && hpText) {
            const hpPercentage = (player.hp / player.maxHp) * 100;
            hpFill.style.width = `${hpPercentage}%`;
            hpText.textContent = `${player.hp}/${player.maxHp}`;
        }

        // Chakra
        chakraSystem.updateChakraUI();

        // XP et niveau
        const levelText = document.getElementById('level-text');
        const xpFill = document.getElementById('xp-fill');
        const xpText = document.getElementById('xp-text');
        
        if (levelText) {
            levelText.textContent = `Niveau ${player.level}`;
        }
        
        if (xpFill && xpText) {
            const xpPercentage = (player.xp / player.xpToNext) * 100;
            xpFill.style.width = `${xpPercentage}%`;
            xpText.textContent = `${player.xp}/${player.xpToNext}`;
        }
    }

    // Mettre à jour la mini-carte
    updateMiniMap() {
        // TODO: Implémenter la mini-carte
        console.log('🗺️ Mise à jour de la mini-carte');
    }

    // Gestion des touches
    handleKeyPress(event) {
        if (this.currentScreen !== 'game-canvas') return;

        switch (event.code) {
            case 'KeyI':
                this.toggleInventory();
                break;
            case 'KeyS':
                this.toggleSkills();
                break;
            case 'KeyM':
                this.toggleMap();
                break;
            case 'Escape':
                this.toggleGameMenu();
                break;
            case 'Space':
                this.handleInteraction();
                break;
            case 'KeyF':
                this.handleScan();
                break;
        }
    }

    // Gestion du mouvement
    handleMovement(direction) {
        if (this.game && this.game.scene.isActive('ExplorationScene')) {
            const scene = this.game.scene.getScene('ExplorationScene');
            if (scene && scene.handleMovement) {
                scene.handleMovement(direction);
            }
        }
    }

    // Basculer l'inventaire
    toggleInventory() {
        console.log('🎒 Inventaire');
        // TODO: Implémenter l'interface d'inventaire
    }

    // Basculer les compétences
    toggleSkills() {
        console.log('⚡ Compétences');
        // TODO: Implémenter l'arbre de compétences
    }

    // Basculer la carte
    toggleMap() {
        console.log('🗺️ Carte');
        // TODO: Implémenter la carte du monde
    }

    // Basculer le menu de jeu
    toggleGameMenu() {
        console.log('⚙️ Menu de jeu');
        // TODO: Implémenter le menu de pause
    }

    // Gérer l'interaction
    handleInteraction() {
        console.log('🤝 Interaction');
        // TODO: Implémenter les interactions
    }

    // Gérer le scan
    handleScan() {
        console.log('👁️ Scan');
        // TODO: Implémenter le scan de zone
    }

    // Afficher une notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Avant la fermeture de la page
    onBeforeUnload(event) {
        if (this.isGameInitialized) {
            // Sauvegarder automatiquement
            saveSystem.quickSave();
        }
    }

    // Nettoyage
    destroy() {
        if (this.game) {
            this.game.destroy(true);
        }
        
        // Nettoyer les systèmes
        if (typeof chakraSystem !== 'undefined') {
            chakraSystem.cleanup();
        }
        
        if (typeof saveSystem !== 'undefined') {
            saveSystem.cleanup();
        }
        
        if (typeof combatSystem !== 'undefined') {
            combatSystem.cleanup();
        }
    }
}

// Initialiser le jeu quand la page est chargée
let shinobiChronicles;

window.addEventListener('load', () => {
    shinobiChronicles = new ShinobiChronicles();
});

// Nettoyage à la fermeture
window.addEventListener('beforeunload', () => {
    if (shinobiChronicles) {
        shinobiChronicles.destroy();
    }
});
