/**
 * Système de gestion du Chakra pour Shinobi Chronicles
 * Inspiré de la Proposition 2 avec améliorations de la Proposition 1
 */

class ChakraSystem {
    constructor() {
        this.regenTimer = null;
        this.lastRegenTime = Date.now();
        this.isRegenerating = false;
        
        this.initializeRegeneration();
        this.setupEventListeners();
    }

    // Initialise la régénération automatique du chakra
    initializeRegeneration() {
        // Régénération toutes les minutes
        this.regenTimer = setInterval(() => {
            this.regenerateChakra();
        }, 60000); // 60 secondes
    }

    // Configure les écouteurs d'événements
    setupEventListeners() {
        // Écouter les changements de visibilité de la page
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.onPageHidden();
            } else {
                this.onPageVisible();
            }
        });

        // Écouter la fermeture de la page
        window.addEventListener('beforeunload', () => {
            this.onPageUnload();
        });
    }

    // Dépense du chakra avec vérifications
    spendChakra(amount, action = "unknown") {
        const player = gameState.player;
        
        if (player.chakra < amount) {
            this.onInsufficientChakra(action, amount);
            return false;
        }

        player.chakra -= amount;
        
        // S'assurer que le chakra ne descend pas en dessous de 0
        if (player.chakra < 0) {
            player.chakra = 0;
        }

        // Arrêter la régénération temporairement après utilisation
        this.pauseRegeneration(5000); // 5 secondes

        // Déclencher les événements liés à la dépense de chakra
        this.onChakraSpent(amount, action);
        
        // Sauvegarder l'état
        gameState.autoSave();
        
        return true;
    }

    // Restaure du chakra
    restoreChakra(amount, source = "natural") {
        const player = gameState.player;
        const oldChakra = player.chakra;
        
        player.chakra = Math.min(player.maxChakra, player.chakra + amount);
        
        const actualRestored = player.chakra - oldChakra;
        
        if (actualRestored > 0) {
            this.onChakraRestored(actualRestored, source);
            gameState.autoSave();
        }
        
        return actualRestored;
    }

    // Régénération naturelle du chakra
    regenerateChakra() {
        const player = gameState.player;
        
        // Ne pas régénérer si déjà au maximum
        if (player.chakra >= player.maxChakra) {
            return;
        }

        // Ne pas régénérer si en combat
        if (this.isInCombat()) {
            return;
        }

        // Calculer la régénération basée sur le temps écoulé
        const now = Date.now();
        const timeDiff = now - this.lastRegenTime;
        const minutesPassed = Math.floor(timeDiff / (1000 * 60));
        
        if (minutesPassed > 0) {
            const baseRegen = 1; // Régénération de base par minute
            let regenAmount = baseRegen * minutesPassed;
            
            // Bonus de régénération selon le clan
            regenAmount = this.applyRegenBonuses(regenAmount);
            
            // Appliquer la régénération
            this.restoreChakra(regenAmount, "regeneration");
            
            this.lastRegenTime = now;
        }
    }

    // Applique les bonus de régénération selon le clan et l'équipement
    applyRegenBonuses(baseRegen) {
        const player = gameState.player;
        let finalRegen = baseRegen;
        
        // Bonus de clan
        const clansData = {
            ryujin: { bonuses: { chakraRegen: 0.1 } },
            yurei: { bonuses: { chakraRegen: 0.2 } },
            kaze: { bonuses: { chakraRegen: 0.15 } }
        };

        const clanData = clansData[player.clan];
        if (clanData && clanData.bonuses.chakraRegen) {
            finalRegen *= (1 + clanData.bonuses.chakraRegen);
        }
        
        // Bonus d'équipement (à implémenter avec le système d'items)
        // finalRegen = this.applyEquipmentBonuses(finalRegen);
        
        // Bonus de lieu (certains endroits régénèrent plus vite)
        finalRegen = this.applyLocationBonuses(finalRegen);
        
        return Math.floor(finalRegen);
    }

    // Bonus de régénération selon le lieu
    applyLocationBonuses(baseRegen) {
        const currentRegion = gameState.world.currentMap;
        
        // Bonus dans les villages et sanctuaires
        const locationBonuses = {
            "konoha_village": 2.0,
            "chakra_shrine": 3.0,
            "meditation_spot": 1.5
        };
        
        const bonus = locationBonuses[currentRegion] || 1.0;
        return baseRegen * bonus;
    }

    // Vérifie si le joueur est en combat
    isInCombat() {
        // Cette fonction sera liée au système de combat
        return window.currentScene && window.currentScene.scene.key === 'BattleScene';
    }

    // Calcule le coût en chakra pour une action
    calculateActionCost(actionType, actionData = {}) {
        const baseCosts = {
            movementCost: 1,
            hazardMovementCost: 5,
            jutsuCosts: {
                basic: 10,
                intermediate: 20,
                advanced: 35,
                ultimate: 50
            }
        };

        switch (actionType) {
            case 'movement':
                return actionData.isHazardous ?
                    baseCosts.hazardMovementCost : baseCosts.movementCost;

            case 'jutsu':
                const jutsuLevel = actionData.level || 'basic';
                return baseCosts.jutsuCosts[jutsuLevel] || baseCosts.jutsuCosts.basic;

            case 'scan':
                return 5; // Coût fixe pour scanner la zone

            case 'stealth':
                return 3; // Coût pour se déplacer furtivement

            default:
                return 1;
        }
    }

    // Gestion du chakra insuffisant
    onInsufficientChakra(action, requiredAmount) {
        const player = gameState.player;
        const missing = requiredAmount - player.chakra;
        
        console.log(`Chakra insuffisant pour ${action}. Manque: ${missing}`);
        
        // Afficher un message à l'utilisateur
        this.showChakraWarning(`Chakra insuffisant! (${missing} manquant)`);
        
        // Proposer des solutions
        this.suggestChakraRestoration();
    }

    // Actions lors de la dépense de chakra
    onChakraSpent(amount, action) {
        // Mettre à jour l'UI
        this.updateChakraUI();
        
        // Effets visuels si nécessaire
        if (gameState.player.chakra < gameState.player.maxChakra * 0.2) {
            this.showLowChakraWarning();
        }
        
        console.log(`Chakra dépensé: ${amount} pour ${action}`);
    }

    // Actions lors de la restauration de chakra
    onChakraRestored(amount, source) {
        // Mettre à jour l'UI
        this.updateChakraUI();
        
        // Effets visuels de restauration
        this.showChakraRestoreEffect(amount);
        
        console.log(`Chakra restauré: ${amount} (${source})`);
    }

    // Met à jour l'interface utilisateur du chakra
    updateChakraUI() {
        const player = gameState.player;
        const chakraFill = document.getElementById('chakra-fill');
        const chakraText = document.getElementById('chakra-text');
        
        if (chakraFill && chakraText) {
            const percentage = (player.chakra / player.maxChakra) * 100;
            chakraFill.style.width = `${percentage}%`;
            chakraText.textContent = `${player.chakra}/${player.maxChakra}`;
            
            // Changer la couleur selon le niveau
            if (percentage < 20) {
                chakraFill.style.backgroundColor = '#e74c3c'; // Rouge
            } else if (percentage < 50) {
                chakraFill.style.backgroundColor = '#f39c12'; // Orange
            } else {
                chakraFill.style.backgroundColor = '#3498db'; // Bleu normal
            }
        }
    }

    // Affiche un avertissement de chakra faible
    showLowChakraWarning() {
        // Créer un effet visuel d'avertissement
        const warning = document.createElement('div');
        warning.className = 'chakra-warning';
        warning.textContent = 'Chakra faible!';
        warning.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 9999;
            animation: pulse 1s infinite;
        `;
        
        document.body.appendChild(warning);
        
        setTimeout(() => {
            document.body.removeChild(warning);
        }, 2000);
    }

    // Affiche un avertissement de chakra insuffisant
    showChakraWarning(message) {
        console.warn(message);
        // Ici on pourrait ajouter une notification UI
    }

    // Suggère des moyens de restaurer le chakra
    suggestChakraRestoration() {
        const suggestions = [
            "Utilisez une Pilule de Chakra",
            "Reposez-vous dans un village",
            "Trouvez un Sanctuaire de Chakra",
            "Attendez la régénération naturelle"
        ];
        
        console.log("Suggestions pour restaurer le chakra:", suggestions);
    }

    // Effet visuel de restauration de chakra
    showChakraRestoreEffect(amount) {
        // Effet visuel simple
        const effect = document.createElement('div');
        effect.textContent = `+${amount} Chakra`;
        effect.style.cssText = `
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translateX(-50%);
            color: #3498db;
            font-weight: bold;
            z-index: 9999;
            pointer-events: none;
            animation: floatUp 2s ease-out forwards;
        `;
        
        document.body.appendChild(effect);
        
        setTimeout(() => {
            document.body.removeChild(effect);
        }, 2000);
    }

    // Pause la régénération temporairement
    pauseRegeneration(duration) {
        this.isRegenerating = false;
        
        setTimeout(() => {
            this.isRegenerating = true;
        }, duration);
    }

    // Gestion de la page cachée (pour calculer le temps offline)
    onPageHidden() {
        this.lastActiveTime = Date.now();
    }

    // Gestion du retour sur la page
    onPageVisible() {
        if (this.lastActiveTime) {
            const offlineTime = Date.now() - this.lastActiveTime;
            const offlineMinutes = Math.floor(offlineTime / (1000 * 60));
            
            if (offlineMinutes > 0) {
                // Appliquer la régénération offline
                const offlineRegen = 1 * offlineMinutes; // 1 chakra par minute
                this.restoreChakra(offlineRegen, "offline_regeneration");
                
                console.log(`Régénération offline: ${offlineRegen} chakra (${offlineMinutes} minutes)`);
            }
        }
    }

    // Gestion de la fermeture de page
    onPageUnload() {
        // Sauvegarder le temps de fermeture pour la régénération offline
        localStorage.setItem('shinobi_last_active', Date.now().toString());
    }

    // Nettoyage du système
    cleanup() {
        if (this.regenTimer) {
            clearInterval(this.regenTimer);
        }
    }

    // Méthodes utilitaires
    getChakraPercentage() {
        const player = gameState.player;
        return (player.chakra / player.maxChakra) * 100;
    }

    isChakraFull() {
        const player = gameState.player;
        return player.chakra >= player.maxChakra;
    }

    isChakraLow(threshold = 0.2) {
        return this.getChakraPercentage() < (threshold * 100);
    }

    canAfford(amount) {
        return gameState.player.chakra >= amount;
    }
}

// Instance globale du système de chakra
const chakraSystem = new ChakraSystem();
