/* Styles principaux pour Shinobi Chronicles */

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

/* Container principal */
#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Écrans du jeu */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.screen.active {
    display: flex;
}

/* Écran de chargement */
#loading-screen {
    background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460);
    flex-direction: column;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

.loading-bar {
    width: 300px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    width: 0%;
    border-radius: 4px;
    animation: loading 3s ease-in-out infinite;
}

/* Menu principal */
#main-menu {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.menu-content {
    text-align: center;
    padding: 2rem;
}

.menu-content h1 {
    font-size: 4rem;
    margin-bottom: 3rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.menu-btn {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #764ba2 0%, #667eea 100%);
}

.menu-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Création de personnage */
#character-creation {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
}

.creation-content {
    max-width: 800px;
    padding: 2rem;
    text-align: center;
}

.creation-content h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #feca57;
}

.clan-selection h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #48dbfb;
}

.clan-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.clan-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.clan-option:hover {
    border-color: #feca57;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.clan-option.selected {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.2);
}

.clan-option h4 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #feca57;
}

.clan-option p {
    font-size: 0.9rem;
    color: #ddd;
    line-height: 1.4;
}

/* Canvas de jeu */
#game-canvas {
    background: #000;
    z-index: 1;
}

/* Animations */
@keyframes glow {
    from {
        text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 107, 107, 0.8), 0 0 40px rgba(254, 202, 87, 0.5);
    }
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .menu-content h1 {
        font-size: 2.5rem;
    }
    
    .creation-content {
        padding: 1rem;
    }
    
    .clan-options {
        grid-template-columns: 1fr;
    }
    
    .menu-btn {
        min-width: 150px;
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .loading-content h1 {
        font-size: 2rem;
    }
    
    .loading-bar {
        width: 250px;
    }
    
    .menu-content h1 {
        font-size: 2rem;
    }
    
    .creation-content h2 {
        font-size: 2rem;
    }
}

/* Utilitaires */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 1s infinite;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification.success {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.notification.error {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.notification.warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.notification.info {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

/* Scrollbars personnalisées */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
}
