/**
 * Configuration principale du jeu Shinobi Chronicles
 * Combine les meilleures idées des deux propositions
 */

const GameConfig = {
    // Configuration Phaser
    phaser: {
        type: Phaser.AUTO,
        width: 1024,
        height: 768,
        parent: 'game-canvas',
        backgroundColor: '#2c3e50',
        physics: {
            default: 'arcade',
            arcade: {
                gravity: { y: 0 },
                debug: false
            }
        },
        scene: [LoadingScene, ExplorationScene, BattleScene, VillageScene]
    },

    // Configuration du monde
    world: {
        tileSize: 32,
        mapWidth: 100,
        mapHeight: 100,
        viewportTiles: 15, // Nombre de tuiles visibles à l'écran
        fogOfWarRadius: 3, // Rayon de révélation autour du joueur
        
        // Régions du jeu (inspiré de la Proposition 1)
        regions: {
            konoha_forest: {
                name: "Forêt de Konoha",
                levelRange: [1, 3],
                startPosition: { x: 25, y: 25 },
                biome: "forest",
                encounterRate: 0.3
            },
            forest_of_whispers: {
                name: "Forêt des Murmures",
                levelRange: [3, 5],
                biome: "dark_forest",
                encounterRate: 0.5,
                unlockLevel: 3
            },
            sand_wastes: {
                name: "Terres Arides",
                levelRange: [5, 8],
                biome: "desert",
                encounterRate: 0.4,
                unlockLevel: 5
            },
            hidden_marshlands: {
                name: "Marécages Cachés",
                levelRange: [8, 10],
                biome: "swamp",
                encounterRate: 0.6,
                unlockLevel: 8
            },
            mountain_shrine: {
                name: "Sanctuaire de la Montagne",
                levelRange: [10, 15],
                biome: "mountain",
                encounterRate: 0.7,
                unlockLevel: 10
            }
        }
    },

    // Configuration des clans (inspiré de la Proposition 1)
    clans: {
        ryujin: {
            name: "Clan Ryūjin",
            description: "Maîtres du jutsu dragon et des techniques de feu",
            element: "fire",
            bonuses: {
                attack: 2,
                fireResistance: 0.2
            },
            startingJutsu: "dragon_slash",
            color: "#e74c3c"
        },
        yurei: {
            name: "Clan Yūrei",
            description: "Spécialistes de la furtivité et des illusions",
            element: "shadow",
            bonuses: {
                stealth: 3,
                genjutsuPower: 0.3
            },
            startingJutsu: "shadow_clone",
            color: "#9b59b6"
        },
        kaze: {
            name: "Clan Kaze",
            description: "Maîtres du vent et de la vitesse",
            element: "wind",
            bonuses: {
                speed: 2,
                windPower: 0.25
            },
            startingJutsu: "wind_cutter",
            color: "#3498db"
        }
    },

    // Système de progression (hybride des deux propositions)
    progression: {
        baseXP: 100,
        xpMultiplier: 1.5,
        maxLevel: 50,
        
        // Rangs ninja (Proposition 1)
        ranks: {
            1: { name: "Genin", title: "Ninja Débutant" },
            5: { name: "Chūnin", title: "Ninja Intermédiaire" },
            10: { name: "Jōnin", title: "Ninja Expert" },
            15: { name: "Jōnin Élite", title: "Ninja Maître" },
            20: { name: "ANBU", title: "Forces Spéciales" },
            30: { name: "Sannin", title: "Ninja Légendaire" },
            50: { name: "Kage", title: "Ombre du Village" }
        },

        // Stats par niveau
        statsPerLevel: {
            hp: 10,
            chakra: 5,
            attack: 2,
            defense: 1
        },

        // Points de compétence
        skillPointsPerLevel: 1,
        bonusSkillPointsAt: [5, 10, 15, 20, 25, 30, 40, 50]
    },

    // Système de chakra (Proposition 2 améliorée)
    chakra: {
        baseAmount: 20,
        regenRate: 1, // Points par minute
        movementCost: 1,
        hazardMovementCost: 5,
        jutsuCosts: {
            basic: 10,
            intermediate: 20,
            advanced: 35,
            ultimate: 50
        }
    },

    // Système de combat (hybride)
    combat: {
        // Éléments et leurs relations
        elements: {
            fire: { weakTo: "water", strongTo: "wind" },
            water: { weakTo: "earth", strongTo: "fire" },
            earth: { weakTo: "lightning", strongTo: "water" },
            lightning: { weakTo: "wind", strongTo: "earth" },
            wind: { weakTo: "fire", strongTo: "lightning" },
            shadow: { weakTo: "light", strongTo: "mind" },
            light: { weakTo: "shadow", strongTo: "dark" }
        },
        
        damageMultipliers: {
            weakness: 1.5,
            resistance: 0.75,
            immunity: 0.1
        },

        // Types d'attaque
        attackTypes: {
            taijutsu: { chakraCost: 5, baseDamage: 1.0 },
            ninjutsu: { chakraCost: 15, baseDamage: 1.3 },
            genjutsu: { chakraCost: 20, baseDamage: 0.8, statusChance: 0.4 }
        }
    },

    // Configuration UI
    ui: {
        hudPosition: "top-left",
        miniMapSize: 150,
        inventorySlots: 20,
        quickSlots: 4,
        
        // Couleurs du thème
        colors: {
            primary: "#2c3e50",
            secondary: "#34495e",
            accent: "#e74c3c",
            success: "#27ae60",
            warning: "#f39c12",
            danger: "#e74c3c",
            hp: "#e74c3c",
            chakra: "#3498db",
            xp: "#f39c12"
        }
    },

    // Configuration de sauvegarde
    save: {
        autoSaveInterval: 30000, // 30 secondes
        maxSaveSlots: 3,
        compressionEnabled: true
    },

    // Configuration audio
    audio: {
        masterVolume: 0.7,
        musicVolume: 0.5,
        sfxVolume: 0.8,
        
        tracks: {
            menu: "audio/music/menu_theme.ogg",
            konoha: "audio/music/village_theme.ogg",
            forest: "audio/music/forest_explore.ogg",
            combat: "audio/music/battle_theme.ogg",
            boss: "audio/music/boss_battle.ogg"
        }
    },

    // Configuration de développement
    debug: {
        enabled: true,
        showFPS: true,
        showHitboxes: false,
        godMode: false,
        unlockAllRegions: false,
        startLevel: 1
    }
};

// Fonctions utilitaires de configuration
GameConfig.getXPForLevel = function(level) {
    if (level <= 1) return 0;
    return Math.floor(this.progression.baseXP * Math.pow(this.progression.xpMultiplier, level - 2));
};

GameConfig.getRankForLevel = function(level) {
    const ranks = Object.keys(this.progression.ranks).map(Number).sort((a, b) => b - a);
    for (let rankLevel of ranks) {
        if (level >= rankLevel) {
            return this.progression.ranks[rankLevel];
        }
    }
    return this.progression.ranks[1];
};

GameConfig.getElementMultiplier = function(attackElement, defenseElement) {
    if (!attackElement || !defenseElement) return 1;
    
    const element = this.combat.elements[attackElement];
    if (!element) return 1;
    
    if (element.strongTo === defenseElement) {
        return this.combat.damageMultipliers.weakness;
    } else if (element.weakTo === defenseElement) {
        return this.combat.damageMultipliers.resistance;
    }
    
    return 1;
};

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameConfig;
}
